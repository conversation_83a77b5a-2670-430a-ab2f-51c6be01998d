"""
邢不行｜策略分享会
选股策略框架𝓟𝓻𝓸

版权所有 ©️ 邢不行
微信: xbx1717

本代码仅供个人学习使用，未经授权不得复制、修改或用于商业用途。

Author: 邢不行
"""
import pandas as pd

fin_cols = []  # 财务因子列，配置后系统会自动加载对应的财务数据
# extra_cols = { 'hk-stock':['收盘价_港股','收盘价_汇率','收盘价_复权_港股']}  # 外部数据，配置后系统自动加载
ov_cols = ['新版申万二级行业名称']  # 全息因子列（可选变量），配置后系统会自动加载对应的全息数据


def add_factor(df: pd.DataFrame, param=None, **kwargs) -> pd.DataFrame:
    # 参数处理
    col_name = kwargs['col_name']

    # 我们这里的市值因子有市值的数据
    df[col_name] = df['新版申万二级行业名称']

    return df[[col_name]]
