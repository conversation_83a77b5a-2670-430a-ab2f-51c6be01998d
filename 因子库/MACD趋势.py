"""
邢不行｜策略分享会
选股策略框架𝓟𝓻𝓸

版权所有 ©️ 邢不行
微信: xbx1717

本代码仅供个人学习使用，未经授权不得复制、修改或用于商业用途。

Author: 邢不行 & 008
"""
import pandas as pd
import numpy as np

# 财务因子列：此列表用于存储财务因子相关的列名称
fin_cols = []  # 财务因子列，配置后系统会自动加载对应的财务数据


def add_factor(df: pd.DataFrame, param=None, **kwargs) -> pd.DataFrame:
    """
    计算MACD趋势因子
    
    MACD（Moving Average Convergence Divergence）指标，用于判断趋势变化。
    结合MACD线、信号线、柱状图来生成趋势信号。
    
    计算公式：
    EMA12 = 12日指数移动平均
    EMA26 = 26日指数移动平均
    DIF = EMA12 - EMA26
    DEA = DIF的9日指数移动平均
    MACD = (DIF - DEA) * 2
    
    :param df: pd.DataFrame，包含单只股票的K线数据
    :param param: 因子计算所需的参数，格式为字典或列表：
                 - 如果是字典：{'fast': 12, 'slow': 26, 'signal': 9}
                 - 如果是列表：[fast, slow, signal]
                 - 如果是None：使用默认参数
    :param kwargs: 其他关键字参数
    :return: pd.DataFrame: 包含新计算的因子列
    """
    
    # ======================== 参数处理 ===========================
    col_name = kwargs['col_name']
    
    if param is None:
        fast = 12
        slow = 26
        signal = 9
    elif isinstance(param, dict):
        fast = param.get('fast', 12)
        slow = param.get('slow', 26)
        signal = param.get('signal', 9)
    elif isinstance(param, (list, tuple)) and len(param) >= 3:
        fast, slow, signal = param[:3]
    else:
        fast = 12
        slow = 26
        signal = 9
    
    # ======================== 计算MACD指标 ===========================
    
    # 计算快速和慢速EMA
    ema_fast = df['收盘价'].ewm(span=fast).mean()
    ema_slow = df['收盘价'].ewm(span=slow).mean()
    
    # 计算DIF线（MACD线）
    dif = ema_fast - ema_slow
    
    # 计算DEA线（信号线）
    dea = dif.ewm(span=signal).mean()
    
    # 计算MACD柱状图
    macd_histogram = (dif - dea) * 2
    
    # ======================== 计算MACD信号 ===========================
    
    # 金叉和死叉
    golden_cross = (dif > dea) & (dif.shift(1) <= dea.shift(1))
    death_cross = (dif < dea) & (dif.shift(1) >= dea.shift(1))
    
    # 零轴突破
    dif_above_zero = (dif > 0) & (dif.shift(1) <= 0)
    dif_below_zero = (dif < 0) & (dif.shift(1) >= 0)
    
    # MACD柱状图变化
    histogram_increasing = macd_histogram > macd_histogram.shift(1)
    histogram_decreasing = macd_histogram < macd_histogram.shift(1)
    
    # 背离检测
    price_high = df['收盘价'].rolling(window=10).max()
    price_low = df['收盘价'].rolling(window=10).min()
    dif_high = dif.rolling(window=10).max()
    dif_low = dif.rolling(window=10).min()
    
    # 顶背离：价格创新高，DIF未创新高
    bearish_divergence = (df['收盘价'] >= price_high) & (dif < dif_high.shift(1))
    # 底背离：价格创新低，DIF未创新低
    bullish_divergence = (df['收盘价'] <= price_low) & (dif > dif_low.shift(1))
    
    # ======================== 计算趋势强度 ===========================
    
    # DIF相对位置（标准化）
    dif_range = dif.rolling(window=50).max() - dif.rolling(window=50).min()
    dif_position = np.where(dif_range != 0, 
                           (dif - dif.rolling(window=50).min()) / dif_range,
                           0.5)
    dif_position = np.clip(dif_position, 0, 1)
    
    # MACD柱状图强度
    histogram_strength = np.tanh(macd_histogram / df['收盘价'].rolling(window=20).std())
    
    # 趋势一致性（DIF和DEA同向）
    trend_consistency = np.where((dif > 0) & (dea > 0), 1,
                                np.where((dif < 0) & (dea < 0), -1, 0))
    
    # ======================== 计算综合趋势因子 ===========================
    
    # 基础趋势信号
    base_signal = np.where(dif > dea, 1, -1) * 0.3
    
    # 金叉死叉信号
    cross_signal = np.where(golden_cross, 0.4,
                           np.where(death_cross, -0.4, 0))
    
    # 零轴突破信号
    zero_cross_signal = np.where(dif_above_zero, 0.3,
                                np.where(dif_below_zero, -0.3, 0))
    
    # 柱状图变化信号
    histogram_signal = np.where(histogram_increasing & (macd_histogram > 0), 0.2,
                               np.where(histogram_decreasing & (macd_histogram < 0), -0.2, 0))
    
    # 背离信号
    divergence_signal = np.where(bullish_divergence, 0.3,
                                np.where(bearish_divergence, -0.3, 0))
    
    # 趋势强度调整
    strength_factor = (dif_position - 0.5) * 2  # 转换为-1到1之间
    
    # 综合因子值
    factor_value = (base_signal + 
                   cross_signal + 
                   zero_cross_signal + 
                   histogram_signal + 
                   divergence_signal) * (1 + strength_factor * 0.2)
    
    # 趋势一致性加权
    factor_value = factor_value * (1 + trend_consistency * 0.1)
    
    # 限制因子值范围
    factor_value = np.clip(factor_value, -2, 2)
    
    # 平滑处理
    factor_value = pd.Series(factor_value, index=df.index).rolling(window=3).mean()
    
    # ======================== 输出结果 ===========================
    
    df[col_name] = factor_value
    df[f'{col_name}_DIF'] = dif
    df[f'{col_name}_DEA'] = dea
    df[f'{col_name}_MACD'] = macd_histogram
    df[f'{col_name}_金叉'] = golden_cross.astype(int)
    df[f'{col_name}_死叉'] = death_cross.astype(int)
    df[f'{col_name}_零轴上穿'] = dif_above_zero.astype(int)
    df[f'{col_name}_零轴下穿'] = dif_below_zero.astype(int)
    df[f'{col_name}_底背离'] = bullish_divergence.astype(int)
    df[f'{col_name}_顶背离'] = bearish_divergence.astype(int)
    df[f'{col_name}_趋势强度'] = strength_factor
    df[f'{col_name}_趋势一致性'] = trend_consistency
    
    return df
