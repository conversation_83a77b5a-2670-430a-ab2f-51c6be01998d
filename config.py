"""
邢不行｜策略分享会
选股策略框架𝓟𝓻𝓸

版权所有 ©️ 邢不行
微信: xbx1717

本代码仅供个人学习使用，未经授权不得复制、修改或用于商业用途。

Author: 邢不行
"""
import os
from pathlib import Path

from core.utils.path_kit import get_folder_path

# ====================================================================================================
# 1️⃣ 回测配置
# ====================================================================================================
# 回测数据的起始时间。如果因子使用滚动计算方法，在回测初期因子值可能为 NaN，实际的首次交易日期可能晚于这个起始时间。
start_date = '2015-01-01'
# 回测数据的结束时间。可以设为 None，表示使用最新数据；也可以指定具体日期，例如 '2024-11-01'。
end_date = '2025-06-16'

# ====================================================================================================
# 2️⃣ 数据配置
# ====================================================================================================
data_center_path = r"E:/quantclass_data"  # 数据中心的文件夹
runtime_data_path = get_folder_path('data')  # 回测结果存放的的文件夹，默认为项目文件夹下的 data 文件夹，可以自定义

# ====================================================================================================
# 3️⃣ 策略配置
# ====================================================================================================
backtest_name = '小市值_周黎明_二级'  # 回测的策略组合的名称。可以自己任意取。一般建议，一个回测组，就是实盘中的一个账户。
# 策略明细
strategy_list = [
#     {
#         "name": "低波大市值",
#         "hold_period": "5D",
#         "offset_list": [0, 1, 2, 3, 4],
#         "select_num": 2,
#         "cap_weight": 1,
#         "rebalance_time": "0955-0955",
#         "factor_list": [("市值", False, "", 1),
#                         ("波动率", True, 250, 1)],
#         "filter_list": [],
#     },
# {
#         "name": "Alpha95策略",
#         "hold_period": "3D",
#         "offset_list": [0, 1, 2],
#         "select_num": 2,
#         "cap_weight": 1,
#         "rebalance_time": "1025-1025",
#         "factor_list": [("成交额Std", True, 5, 1)],
#         "filter_list": [
#             ("成交额Mean", 5, "val:>=5000_0000", True),
#             ("近期停牌天数", 5, "val:<1", True),
#             ("收盘价", "", "val:<15", True),
#         ],
#         "timing": {
#             'name': '定风波2择时',  # 择时策略名称
#             'limit': 0.2,
#             'factor_list': [('开盘至今涨幅', False, None, 60, '1015'),
#                             ('隔夜涨跌幅', False, None, 60, '0935')],
#             'params': (0.8, 1.1)
#         },
#     },
    {
        "name": "小市值_周黎明",
        "hold_period": "3D",
        "offset_list": [0, 1, 2],
        "select_num": 5,
        "cap_weight": 1,
        "rebalance_time": "0955-0955",
        "factor_list": [
            ("Ret", False, 5, 100),
            ("Ret", False, 20, 0.2),
            ("二级行业", False, "", 2),
            ("市值", True, "", 1),
        ],
        "filter_list": [
            # ("成交额Mean", 5, "val:>=5000_0000", True)
        ],
        "timing": {
            "name": "定风波2择时",
            "limit": 0.2,
            'factor_list': [('开盘至今涨幅', False, None, 60, '1015'),
                            ('隔夜涨跌幅', False, None, 60, '0935')],
            'params': (0.8, 1.1)
        },
    },
#     {
#         "name": "小市值_量价优化",
#         "hold_period": "3D",
#         "offset_list": [0, 1, 2],
#         "select_num": 2,
#         "cap_weight": 1,
#         "rebalance_time": "1025-1025",
#         "factor_list": [
#             ("Ret", True, 20, 1),
#             ("市值", True, "", 1),
#             ("成交额Std", True, 5, 1),
#         ],
#         "filter_list": [("成交额Mean", 5, "val:>=5000_0000", True)],
#         "timing": {
#             'name': '定风波2择时',  # 择时策略名称
#             'limit': 0.2,
#             'factor_list': [('开盘至今涨幅', False, None, 60, '1015'),
#                             ('隔夜涨跌幅', False, None, 60, '0935')],
#             'params': (0.8, 1.1)
#         },
#     },
#     {
#         "name": "中等生策略",
#         "hold_period": "3D",
#         "offset_list": [0, 1, 2],
#         "select_num": 3,
#         "cap_weight": 1,
#         "rebalance_time": "0955-0955",
#         "factor_list": [
#             ("指数相关性", False, ["sh000300", 20], 1),
#             ("指数相关性", False, ["sh932000", 20], 1),
#             ("指数相关性", False, ["sh000001", 20], 1),
#             ("指数相关性", False, ["sh000016", 20], 1),
#             ("指数相关性", False, ["sh000852", 20], 1),
#             ("指数相关性", False, ["sh000905", 20], 1),
#             ("指数相关性", False, ["sz399006", 20], 1),
#         ],
#         "filter_list": [("收盘价", "", "val:<15", True)],
#         "timing": {
#             "name": "定风波择时",
#             "limit": 200,
#             "factor_list": [("开盘至今涨幅", False, None, 1, "0945")],
#             "params": 0.4,
#         },
#     },
#     {
#         "name": "镜重圆",
#         "hold_period": "3D",
#         "offset_list": [0, 1, 2],
#         "select_num": 2,
#         "cap_weight": 1,
#         "rebalance_time": "1025-1025",
#         "factor_list": [("净利润_单季同比", False, "", 1), ("ROE", False, "单季", 1)],
#         "filter_list": [("BP", "", "val:>1", False)],
#         "timing": {
#             "name": "早盘强度动态阈值",
#             "limit": 0.2,
#             "factor_list": [("开盘至今涨幅", False, None, 1, "1015")],
#             "params": 60,
#         },
#     },
#     {
#         'name': '小市值_基本面优化',
#         'hold_period': '3D',
#         'offset_list': [0, 1, 2],
#         'select_num': 2,
#         'cap_weight': 1,
#         'rebalance_time': '1035-1035',
#         'factor_list': [('市值', True, '', 1),
#                         ('归母净利润同比增速', False, 60, 1), ],
#         'filter_list': [('ROE', '单季', 'pct:<=0.8', False),
#                         # ('成交额Mean', 5, 'val:>=5000_0000', True),
#                         ],
#         "timing": {
#             'name': '定风波2择时',  # 择时策略名称
#             'limit': 0.2,
#             'factor_list': [('开盘至今涨幅', False, None, 60, '1025'),
#                             ('隔夜涨跌幅', False, None, 60, '0935')],
#             'params': (0.8, 1.1)
#         },
#     },
#     {
#         "name": "无人问津_小市值_改进版",  # 策略名
#         "hold_period": '3D',  # 持仓周期，W 代表周，M 代表月
#         "offset_list": [0, 1, 2],
#         "select_num": 2,  # 选股数量，可以是整数，也可以是小数，比如 0.1 表示选取 10% 的股票
#         "cap_weight": 1,
#         "rebalance_time": "1025-1025",
#         "factor_list": [  # 选股因子列表
#             ('市值', True, None, 1),
#         ],
#         "filter_list": [
#             ('ROE','单季','pct:>0.7',True),
#             ('换手率_总',10,'val:<0.02',True),
#             ('N日换手率标准差同比',10,'val:<1',True),
#             ('相对收益率排位',10,'val:>0.5',True),
#             ("成交额Mean", 5, "val:>=1000_0000", True),  # 过滤成交额小于1000万的股票
#         ], # 过滤因子列表
#         "timing": {
#             'name': '定风波2择时',  # 择时策略名称
#             'limit': 0.2,
#             'factor_list': [('开盘至今涨幅', False, None, 60, '1015'),
#                             ('隔夜涨跌幅', False, None, 60, '0935')],
#             'params': (0.8, 1.1)
#         },
#     },
#     {
#         "name": "KDJ_CMO熊市策略",  # 008专属策略：结合KDJ和CMO的熊市超额收益策略
#         "hold_period": '3D',  # 持仓周期3天
#         "offset_list": [0, 1, 2],
#         "select_num": 3,  # 选股数量3只，分散风险
#         "cap_weight": 1,
#         "rebalance_time": "1030-1030",  # 10:30换仓，避开开盘波动
#         "factor_list": [  # 选股因子列表
#             # ('KDJ_CMO_信号', False, {'kdj_period': 5, 'cmo_period': 7, 'j_upper': 90, 'j_lower': 30}, 1),  # 主要信号因子，权重2
#             ('市值', True, None, 1),  # 小市值偏好，权重1
#             ('波动率', True, 20, 1),  # 低波动率偏好，权重0.5
#         ],
#         "filter_list": [
#             ("成交额Mean", 10, "val:>=3000_0000", True),  # 过滤成交额小于3000万的股票，确保流动性
#             ('近期停牌天数', 5, 'val:<1', True),  # 过滤近期停牌的股票
#             ('收盘价', '', 'val:>=2', True),  # 过滤低价股，避免退市风险
#             ('收盘价', '', 'val:<=50', True),  # 过滤高价股，保持策略一致性
#         ],
#         "timing": {
#             'name': '定风波2择时',  # 择时策略名称
#             'limit': 0.3,  # 择时范围扩大到30%，增加选股灵活性
#             'factor_list': [('开盘至今涨幅', False, None, 60, '1025'),  # 使用10:25的涨幅数据
#                             ('隔夜涨跌幅', False, None, 60, '0935')],
#             'params': (0.7, 1.2)  # 调整择时参数，适应熊市环境
#         },
#     },
#     {
#         "name": "KDJ_CMO增强策略_V2",  # 008优化策略：多因子增强版本
#         "hold_period": '5D',  # 延长持仓周期到5天，减少交易频率
#         "offset_list": [0, 1, 2, 3, 4],
#         "select_num": 5,  # 增加选股数量到5只，进一步分散风险
#         "cap_weight": 1,
#         "rebalance_time": "1000-1000",  # 10:00换仓，避开开盘和尾盘波动
#         "factor_list": [  # 多因子组合
#             ('KDJ_CMO_增强信号', False, {'kdj_period': 9, 'cmo_period': 14, 'j_upper': 75, 'j_lower': 25, 'vol_period': 20, 'trend_period': 5}, 3),  # 主信号因子，权重3
#             ('RSI动量', False, {'period': 14, 'smooth': 3}, 2),  # RSI动量因子，权重2
#             ('MACD趋势', False, {'fast': 12, 'slow': 26, 'signal': 9}, 1.5),  # MACD趋势因子，权重1.5
#             ('市值', True, None, 1),  # 小市值偏好，权重1
#             ('波动率', True, 30, 0.8),  # 低波动率偏好，权重0.8
#         ],
#         "filter_list": [
#             ("成交额Mean", 20, "val:>=5000_0000", True),  # 提高流动性要求到5000万
#             ('近期停牌天数', 10, 'val:<1', True),  # 扩大停牌检查周期
#             ('收盘价', '', 'val:>=3', True),  # 提高最低价格要求
#             ('收盘价', '', 'val:<=100', True),  # 放宽最高价格限制
#             ('换手率', 5, 'val:>=0.005', True),  # 增加最低换手率要求，确保活跃度
#             ('Ret', 20, 'pct:>=0.1', True),  # 过滤表现过差的股票（20日收益率排名后10%）
#         ],
#         "timing": {
#             'name': '定风波2择时',  # 择时策略名称
#             'limit': 0.4,  # 进一步扩大择时范围到40%
#             'factor_list': [('开盘至今涨幅', False, None, 60, '1000'),  # 使用10:00的涨幅数据
#                             ('隔夜涨跌幅', False, None, 60, '0935')],
#             'params': (0.6, 1.3)  # 更宽松的择时参数
#         },
#     },
#     {
#         "name": "KDJ_CMO保守策略_V2",  # 008保守策略：低回撤版本
#         "hold_period": '7D',  # 更长持仓周期，降低交易频率
#         "offset_list": [0, 1, 2, 3, 4, 5, 6],
#         "select_num": 8,  # 更多选股数量，最大化分散风险
#         "cap_weight": 1,
#         "rebalance_time": "1015-1015",  # 10:15换仓
#         "factor_list": [  # 保守型因子组合
#             ('KDJ_CMO_增强信号', False, {'kdj_period': 14, 'cmo_period': 20, 'j_upper': 70, 'j_lower': 30, 'vol_period': 30, 'trend_period': 10}, 2),  # 更保守的参数
#             ('RSI动量', False, {'period': 20, 'smooth': 5}, 1.5),  # 更长周期的RSI
#             ('MACD趋势', False, {'fast': 12, 'slow': 26, 'signal': 9}, 1),  # MACD趋势确认
#             ('市值', True, None, 1.5),  # 加强小市值偏好
#             ('波动率', True, 60, 2),  # 强化低波动率要求
#             ('ROE', False, '单季', 0.5),  # 增加基本面因子
#         ],
#         "filter_list": [
#             ("成交额Mean", 30, "val:>=8000_0000", True),  # 更高流动性要求
#             ('近期停牌天数', 15, 'val:<1', True),  # 更长期的停牌检查
#             ('收盘价', '', 'val:>=5', True),  # 更高的价格下限
#             ('收盘价', '', 'val:<=80', True),  # 适中的价格上限
#             ('换手率', 10, 'val:>=0.008', True),  # 更高的换手率要求
#             ('Ret', 60, 'pct:>=0.2', True),  # 过滤长期表现差的股票
#             ('ROE', '单季', 'pct:>=0.3', True),  # 基本面过滤
#         ],
#         "timing": {
#             'name': '定风波2择时',  # 择时策略名称
#             'limit': 0.5,  # 最大择时范围
#             'factor_list': [('开盘至今涨幅', False, None, 60, '1015'),
#                             ('隔夜涨跌幅', False, None, 60, '0935')],
#             'params': (0.5, 1.4)  # 最宽松的择时参数
#         },
#     }
#     {
#         'name': '平均市值RET策略',
#         'hold_period': '3D',
#         'offset_list': [0, 1, 2],
#         'select_num': 3,
#         'cap_weight': 1,
#         'rebalance_time': '0955-0955',
#         'factor_list': [('平均市值', True, 10, 0.9),
#                         ('Ret', True, 10, 0.1), ],
#         'filter_list': [
#                         ('近期涨跌幅', 1, 'val:>=-0.09'),
#                         ('成交额Std',15,'pct:<0.9',True),
#                         ],
#         "timing": {
#             'name': '定风波2择时',  # 择时策略名称
#             'limit': 0.2,
#             'factor_list': [('开盘至今涨幅', False, None, 60, '0955'),
#                             ('隔夜涨跌幅', False, None, 60, '0935')],
#             'params': (0.8, 1.1)
#         },
#     },
    # {
    #     'name': '成交额缩波优化',
    #     'hold_period': '3D',
    #     'offset_list': [0, 1, 2],
    #     'select_num': 3,
    #     'cap_weight': 1,
    #     'rebalance_time': '0955-0955',
    #     'factor_list': [('成交额因子优化1', True, (10, 135), 3),
    #                     ('bias', True, 10, 1),
    #                     ('Ret', True, 30, 1),],
    #     'filter_list': [
    #         ('市值', None, 'val:<3e9', True),
    #         ('收盘价', '', 'val:>=5', True),  # 更高的价格下限
    #     ],
    #     "timing": {
    #         'name': '定风波2择时',  # 择时策略名称
    #         'limit': 0.2,
    #         'factor_list': [('开盘至今涨幅', False, None, 60, '0955'),
    #                         ('隔夜涨跌幅', False, None, 60, '0935')],
    #         'params': (0.8, 1.1)
    #     },
    # },
]

# excluded_boards = []  # 排除板块，比如 cyb 表示创业板，kcb 表示科创板
excluded_boards = ["kcb","bj"]  # 同时过滤创业板和科创板

# 上市至今交易天数
days_listed = 250
# 整体资金使用率，也就是用于模拟的资金比例
total_cap_usage = 100 / 100  # 100%表示用全部的资金买入，如果是0.5就是使用一半的资金来模拟交易

# ====================================================================================================
# 4️⃣ 模拟交易配置
# 以下参数几乎不需要改动
# ====================================================================================================
initial_cash = 10_0000  # 初始资金10w
# initial_cash = 1_0000_0000  # 初始资金10w
# 手续费
c_rate = 1.2 / 10000
# 印花税
t_rate = 1 / 1000

# ====================================================================================================
# 5️⃣ 其他配置
# 以下参数几乎不需要改动
# ====================================================================================================
n_jobs = os.cpu_count() - 1

# ==== factor_col_limit 介绍 ====
factor_col_limit = 32  # 内存优化选项，一次性计算多少列因子。8 是16G电脑的推荐配置
# - 数字越大，计算速度越快，但同时内存占用也会增加。
# - 该数字是在 "因子数量 * 参数数量" 的基础上进行优化的。
#   - 例如，当你遍历 200 个因子，每个因子有 10 个参数，总共生成 2000 列因子。
#   - 如果 `factor_col_limit` 设置为 64，则计算会拆分为 ceil(2000 / 64) = 32 个批次，每次最多处理 64 列因子。
# - 以上数据仅供参考，具体值会根据机器配置、策略复杂性、回测周期等有所不同。建议大家根据实际情况，逐步测试自己机器的性能极限，找到适合的最优值。

# =====参数预检查=====
runtime_folder = get_folder_path(runtime_data_path, '运行缓存')
if Path(data_center_path).exists() is False:
    print(f'数据中心路径不存在：{data_center_path}，请检查配置或联系助教，程序退出')
    exit()

# 强制转换为 Path 对象
data_center_path = Path(data_center_path)
runtime_data_path = Path(runtime_data_path)
