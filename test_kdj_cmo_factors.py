"""
测试KDJ和CMO因子的脚本

Author: 008
"""
import pandas as pd
import numpy as np
import sys
from pathlib import Path

# 添加项目路径到sys.path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入因子模块
from 因子库.KDJ import add_factor as kdj_add_factor
from 因子库.CMO import add_factor as cmo_add_factor
from 因子库.KDJ_CMO_信号 import add_factor as signal_add_factor


def create_test_data():
    """创建测试数据"""
    # 创建一个简单的股票数据用于测试
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    
    # 模拟股票价格数据
    np.random.seed(42)  # 设置随机种子以确保结果可重现
    
    # 生成基础价格序列
    base_price = 10.0
    price_changes = np.random.normal(0, 0.02, 100)  # 日收益率，均值0，标准差2%
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 0.1))  # 确保价格不为负
    
    # 生成开高低收数据
    df = pd.DataFrame({
        '交易日期': dates,
        '股票代码': 'sh600000',
        '股票名称': '测试股票',
        '收盘价': prices,
    })
    
    # 生成开盘价、最高价、最低价
    df['开盘价'] = df['收盘价'].shift(1).fillna(df['收盘价'].iloc[0])
    df['最高价'] = df[['开盘价', '收盘价']].max(axis=1) * (1 + np.random.uniform(0, 0.03, len(df)))
    df['最低价'] = df[['开盘价', '收盘价']].min(axis=1) * (1 - np.random.uniform(0, 0.03, len(df)))
    
    # 生成成交量和成交额
    df['成交量'] = np.random.randint(1000000, 10000000, len(df))
    df['成交额'] = df['成交量'] * df['收盘价']
    
    return df


def test_kdj_factor():
    """测试KDJ因子"""
    print("=" * 50)
    print("测试KDJ因子")
    print("=" * 50)
    
    df = create_test_data()
    
    # 测试KDJ因子
    result_df = kdj_add_factor(df.copy(), param=9, col_name='KDJ_测试')
    
    # 检查结果
    print(f"数据行数: {len(result_df)}")
    print(f"KDJ相关列: {[col for col in result_df.columns if 'KDJ' in col]}")
    
    # 显示最后10行的KDJ数据
    kdj_cols = ['交易日期', '收盘价', 'KDJ_测试_K', 'KDJ_测试_D', 'KDJ_测试_J', 'KDJ_测试']
    print("\n最后10行KDJ数据:")
    print(result_df[kdj_cols].tail(10))
    
    # 检查数据有效性
    print(f"\nKDJ_K 范围: {result_df['KDJ_测试_K'].min():.2f} ~ {result_df['KDJ_测试_K'].max():.2f}")
    print(f"KDJ_D 范围: {result_df['KDJ_测试_D'].min():.2f} ~ {result_df['KDJ_测试_D'].max():.2f}")
    print(f"KDJ_J 范围: {result_df['KDJ_测试_J'].min():.2f} ~ {result_df['KDJ_测试_J'].max():.2f}")
    
    return result_df


def test_cmo_factor():
    """测试CMO因子"""
    print("\n" + "=" * 50)
    print("测试CMO因子")
    print("=" * 50)
    
    df = create_test_data()
    
    # 测试CMO因子
    result_df = cmo_add_factor(df.copy(), param=14, col_name='CMO_测试')
    
    # 检查结果
    print(f"数据行数: {len(result_df)}")
    print(f"CMO相关列: {[col for col in result_df.columns if 'CMO' in col]}")
    
    # 显示最后10行的CMO数据
    cmo_cols = ['交易日期', '收盘价', 'CMO_测试', 'CMO_测试_多头', 'CMO_测试_空头']
    print("\n最后10行CMO数据:")
    print(result_df[cmo_cols].tail(10))
    
    # 检查数据有效性
    print(f"\nCMO 范围: {result_df['CMO_测试'].min():.2f} ~ {result_df['CMO_测试'].max():.2f}")
    print(f"CMO 多头信号数量: {result_df['CMO_测试_多头'].sum()}")
    print(f"CMO 空头信号数量: {result_df['CMO_测试_空头'].sum()}")
    
    return result_df


def test_signal_factor():
    """测试KDJ_CMO信号因子"""
    print("\n" + "=" * 50)
    print("测试KDJ_CMO信号因子")
    print("=" * 50)
    
    df = create_test_data()
    
    # 测试信号因子
    param = {'kdj_period': 9, 'cmo_period': 14, 'j_upper': 80, 'j_lower': 20}
    result_df = signal_add_factor(df.copy(), param=param, col_name='KDJ_CMO_信号_测试')
    
    # 检查结果
    print(f"数据行数: {len(result_df)}")
    signal_cols = [col for col in result_df.columns if 'KDJ_CMO_信号' in col]
    print(f"信号相关列: {signal_cols}")
    
    # 显示最后10行的信号数据
    display_cols = ['交易日期', '收盘价', 'KDJ_CMO_信号_测试', 'KDJ_CMO_信号_测试_J值', 
                   'KDJ_CMO_信号_测试_CMO值', 'KDJ_CMO_信号_测试_买入信号', 'KDJ_CMO_信号_测试_卖出信号']
    print("\n最后10行信号数据:")
    print(result_df[display_cols].tail(10))
    
    # 统计信号情况
    buy_signals = result_df['KDJ_CMO_信号_测试_买入信号'].sum()
    sell_signals = result_df['KDJ_CMO_信号_测试_卖出信号'].sum()
    
    print(f"\n信号统计:")
    print(f"买入信号数量: {buy_signals}")
    print(f"卖出信号数量: {sell_signals}")
    print(f"因子值范围: {result_df['KDJ_CMO_信号_测试'].min():.4f} ~ {result_df['KDJ_CMO_信号_测试'].max():.4f}")
    
    # 显示有信号的日期
    buy_dates = result_df[result_df['KDJ_CMO_信号_测试_买入信号'] == 1]['交易日期'].tolist()
    sell_dates = result_df[result_df['KDJ_CMO_信号_测试_卖出信号'] == 1]['交易日期'].tolist()
    
    if buy_dates:
        print(f"\n买入信号日期: {[d.strftime('%Y-%m-%d') for d in buy_dates[:5]]}{'...' if len(buy_dates) > 5 else ''}")
    if sell_dates:
        print(f"卖出信号日期: {[d.strftime('%Y-%m-%d') for d in sell_dates[:5]]}{'...' if len(sell_dates) > 5 else ''}")
    
    return result_df


def main():
    """主函数"""
    print("开始测试KDJ和CMO因子...")
    
    try:
        # 测试各个因子
        kdj_result = test_kdj_factor()
        cmo_result = test_cmo_factor()
        signal_result = test_signal_factor()
        
        print("\n" + "=" * 50)
        print("所有测试完成！")
        print("=" * 50)
        print("✅ KDJ因子测试通过")
        print("✅ CMO因子测试通过") 
        print("✅ KDJ_CMO信号因子测试通过")
        print("\n因子已准备就绪，可以在策略中使用！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
