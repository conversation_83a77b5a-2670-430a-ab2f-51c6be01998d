"""
邢不行｜策略分享会
选股策略框架𝓟𝓻𝓸

版权所有 ©️ 邢不行
微信: xbx1717

本代码仅供个人学习使用，未经授权不得复制、修改或用于商业用途。

Author: 邢不行
"""
import shutil
import time

from config import runtime_folder, n_jobs
from core.data_center import prepare_data
from core.equity import simulate_performance
from core.model.backtest_config import BacktestConfig, BacktestConfigFactory
from core.select_stock import calculate_factors, select_stocks, concat_select_results
from core.utils.log_kit import divider, logger

# ====================================================================================================
# 模拟交易
# 1. 计算初始资金曲线
# 2. 保存回测结果，包括资金曲线和各项收益评价指标
# 3. 可选：显示资金曲线图表
# 4. 如有择时信号，执行再择时模拟并保存结果
# ====================================================================================================
pass

# ====================================================================================================
# 动态杠杆再择时模拟
# 1. 生成动态杠杆
# 2. 进行动态杠杆再择时的回测模拟
# 3. 保存结果
# ====================================================================================================
pass


# ====================================================================================================
# ** 回测主程序 **
# 1. 准备工作
# 2. 读取数据
# 3. 计算因子
# 4. 选股
# 5. 整理选股数据
# 6. 添加下一个每一个周期需要卖出的币的信息
# 7. 计算资金曲线
# ====================================================================================================
def run_backtest(conf: BacktestConfig):
    """
    ** 回测主程序流程 **
    本脚本执行以下步骤：
    0. 初始配置与准备
    1. 数据准备：加载并预处理回测所需数据
    2. 因子计算：计算用于选股的因子
    3. 选股：基于因子结果筛选目标股票
    4. 实盘模拟：模拟投资组合的表现，生成资金曲线
    """
    # ====================================================================================================
    # 1. 读取回测所需数据，并做简单的预处理
    # - 加载历史行情数据或其他相关数据
    # ====================================================================================================
    # 调用数据准备函数，加载并预处理回测所需的数据
    divider('准备数据', '-')

    # 缓存当前的config
    conf.save()

    logger.debug(f'🧹 删除运行缓存文件夹：{runtime_folder}')
    shutil.rmtree(runtime_folder, ignore_errors=True)
    runtime_folder.mkdir(parents=True, exist_ok=True)
    prepare_data(conf)

    # ====================================================================================================
    # 2. 因子计算
    # ====================================================================================================
    # 根据配置文件计算策略因子和过滤因子
    divider('因子计算', '-')
    calculate_factors(conf)

    # ====================================================================================================
    # 3. 选股
    # ====================================================================================================
    # 根据计算得到的因子进行选股
    divider('条件选股', '-')
    s_time = time.time()
    select_stocks(conf)
    select_results = concat_select_results(conf)  # 合并多个策略的选股结果

    logger.debug(f'💾 选股结果数据大小：{select_results.memory_usage(deep=True).sum() / 1024 / 1024:.4f} MB')
    logger.ok(f'选股完成，总耗时：{time.time() - s_time:.3f}秒')

    # ====================================================================================================
    # 4. 实盘模拟资金曲线
    # ====================================================================================================
    # 基于选股结果，模拟投资组合表现并生成资金曲线
    divider('模拟交易', '-')
    simulate_performance(conf)


# ====================================================================================================
# ** 批量回测 **
# 1. 准备工作：清理环境，初始化参数配置
# 2. 读取数据：加载回测所需的行情数据
# 3. 计算因子：根据策略要求计算选股因子
# 4. 选股和计算资金曲线：并行回测选股结果并生成资金曲线
# 5. 针对选股结果进行聚合
# ====================================================================================================
def run_backtest_multi(factory: BacktestConfigFactory, boost=True):
    # ====================================================================================================
    # 1. 准备工作
    # ====================================================================================================
    iter_results_folder = factory.result_folder

    # 删除缓存
    shutil.rmtree(iter_results_folder, ignore_errors=True)
    iter_results_folder.mkdir(parents=True, exist_ok=True)

    conf_list = factory.config_list
    for index, conf in enumerate(conf_list):
        logger.debug(f'ℹ️ 策略{index + 1}｜共{len(conf_list)}个')
        logger.debug(f'{conf.get_fullname()}')
        conf.save()
    logger.ok('策略池中需要回测的策略数：{}'.format(len(conf_list)))

    # 记录一下时间戳
    r_time = time.time()

    # ====================================================================================================
    # 2. 读取回测所需数据，并做简单的预处理
    # ====================================================================================================
    divider('读取数据', sep='-')
    conf_all = factory.generate_all_factor_config()
    prepare_data(conf_all, boost=True)

    # ====================================================================================================
    # 3. 计算因子
    # ====================================================================================================
    divider('因子计算', sep='-')
    calculate_factors(conf_all, boost=True)

    # ====================================================================================================
    # 4. 选股
    # - 注意：选完之后，每一个策略的选股结果会被保存到硬盘
    # ====================================================================================================
    divider('条件选股', sep='-')
    s_time = time.time()
    logger.debug(f'注意：这个过程时间久，和包含的策略及子策略数量、选股数量有关...')

    logger.debug(f'🚀 多进程选股，进程数量：{n_jobs}。要是长期卡住，建议先单进程测试' if boost else '🚲 单进程选股')
    if boost:
        select_stocks(factory.config_list, boost=False)
    else:
        for conf in factory.config_list:
            logger.info(f'{conf.name}的{len(conf.strategy_list)}个子策略选股，并行任务数：{n_jobs}')
            select_stocks(conf, boost=False)  # 选股

    logger.ok(f'完成选股，花费时间：{time.time() - s_time:.3f}秒，累计时间：{(time.time() - r_time):.3f}秒')

    # ====================================================================================================
    # 5. 针对选股结果进行聚合
    # ====================================================================================================
    divider('子策略模拟', sep='-')
    logger.debug(f'注意：主要和选股数量有关...')
    s_time = time.time()
    report_list = []

    # 串行
    for conf in conf_list:
        logger.debug(f"🔃 聚合{conf.name}的{len(conf.strategy_list)}个子策略，并计算资金曲线...")
        concat_select_results(conf)
        report_list.append(simulate_performance(conf, show_plot=False))

    if len(report_list) > 65535:
        logger.debug(f'回测报表数量为 {len(report_list)}，超过 65535，后续可能会占用海量内存')
    logger.ok(f'回测模拟已完成，花费时间：{time.time() - s_time:.3f}秒，累计时间：{(time.time() - r_time):.3f}秒')

    return report_list
