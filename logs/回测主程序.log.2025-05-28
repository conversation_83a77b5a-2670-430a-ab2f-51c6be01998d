2025-05-28 14:59:21,525 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 14:59:21,547 [DEBUG] - # VERSION: select-stock-pro(1.4.2.timing)
2025-05-28 14:59:21,547 [DEBUG] - # BUILD VERSION: v1.4.2.timing.20250418
2025-05-28 14:59:21,547 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 14:59:21,579 [DEBUG] - --------------------------- 准备数据 2025-05-28 14:59:21 ---------------------------
2025-05-28 14:59:21,602 [DEBUG] - 🧹 删除运行缓存文件夹：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存
2025-05-28 14:59:21,604 [INFO] - 读取数据中心数据...
2025-05-28 14:59:21,604 [DEBUG] - 🛂 没有因子需要额外的全息字段
2025-05-28 14:59:21,604 [DEBUG] - 🗑️ [策略配置] 需要排除`科创板`
2025-05-28 14:59:21,604 [DEBUG] - 🕒 检测到需要分钟数据：['1030']，需要额外准备pivot数据
2025-05-28 14:59:21,618 [DEBUG] - 📂 读取到股票数量：5132，不包括科创板
2025-05-28 14:59:21,635 [DEBUG] - 🚀 多进程处理数据，进程数量：23
2025-05-28 15:00:04,459 [DEBUG] - 📈 保存股票预处理数据: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\股票预处理数据.pkl
2025-05-28 15:00:04,460 [DEBUG] - 📅 行情数据最新交易日期：2025-05-27 00:00:00
2025-05-28 15:00:08,436 [DEBUG] - 📄 生成行情数据透视表...
2025-05-28 15:00:08,436 [DEBUG] - ⚗️ 合成整体市场数据...
2025-05-28 15:00:10,889 [DEBUG] - [1/4] 开盘价透视表...
2025-05-28 15:00:13,978 [DEBUG] - [2/4] 收盘价透视表...
2025-05-28 15:00:17,006 [DEBUG] - [3/4] 前收盘价透视表...
2025-05-28 15:00:20,151 [DEBUG] - [4/4] 1030透视表...
2025-05-28 15:00:23,456 [DEBUG] - 🗄️ 保存行情数据透视表: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\全部股票行情pivot.pkl
2025-05-28 15:00:23,888 [OK] - 数据准备耗时：62.28 秒
2025-05-28 15:00:24,913 [DEBUG] - --------------------------- 因子计算 2025-05-28 15:00:24 ---------------------------
2025-05-28 15:00:24,934 [INFO] - 因子计算...
2025-05-28 15:00:24,934 [DEBUG] - 🛂 配置信息检查...
2025-05-28 15:00:24,934 [DEBUG] - ℹ️ 检测到没有财务因子
2025-05-28 15:00:24,934 [DEBUG] - 🔍 检测到外部数据：['5min_close']
2025-05-28 15:00:24,935 [DEBUG] - 💿 读取股票K线数据...
2025-05-28 15:00:29,930 [DEBUG] - * 总共计算因子个数：8 个
* 单次计算因子个数：32 个，(需分成1组计算)
* 需要计算币种数量：5077 个
2025-05-28 15:00:29,930 [DEBUG] - 🚀 多进程计算因子，进程数量：23
2025-05-28 15:00:29,930 [DEBUG] - 🗂️ 因子分片计算中，进度：1/1
2025-05-28 15:01:56,048 [DEBUG] - 📅 因子结果最晚日期：2025-05-27 00:00:00
2025-05-28 15:01:58,651 [DEBUG] - 💾 存储因子数据...
2025-05-28 15:01:58,651 [DEBUG] - - D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\all_factors_kline.pkl
2025-05-28 15:01:58,661 [DEBUG] - 最晚交易日期：2025-05-27 00:00:00
2025-05-28 15:02:01,045 [OK] - 因子计算完成，耗时：96.11秒
2025-05-28 15:02:02,481 [DEBUG] - --------------------------- 条件选股 2025-05-28 15:02:02 ---------------------------
2025-05-28 15:02:02,519 [DEBUG] - 🔍 因子文件：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\all_factors_kline.pkl
2025-05-28 15:02:04,097 [DEBUG] - 🎯 #0.KDJ_CMO熊市策略 选股启动...
2025-05-28 15:02:04,561 [DEBUG] - 📦 [#0.KDJ_CMO熊市策略] 选股数据加载完成，最晚日期：2025-05-27 00:00:00
2025-05-28 15:02:06,879 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 数据清洗完成，去掉空因子数据，最晚日期：2025-05-27 00:00:00
2025-05-28 15:02:07,824 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 前置筛选耗时：0.94s。数据最晚日期：2025-05-27 00:00:00
2025-05-28 15:02:15,561 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 选股复合因子计算耗时：7.73s。数据最晚日期：2025-05-27 00:00:00
2025-05-28 15:02:19,128 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 定风波择时：3.57s
2025-05-28 15:02:23,132 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 选股耗时：4.00s。数据最晚日期：2025-05-27 00:00:00
2025-05-28 15:02:23,161 [DEBUG] - 🏁 [#0.KDJ_CMO熊市策略] 选股耗时: 19.06s
2025-05-28 15:02:23,457 [DEBUG] - 🔍 计算`#0.KDJ_CMO熊市策略`最新选股结果, 数据最晚选股日：2025-05-27 00:00:00
2025-05-28 15:02:23,491 [DEBUG] - 💾 选股结果数据大小：1.9161 MB
2025-05-28 15:02:23,491 [OK] - 选股完成，总耗时：20.989秒
2025-05-28 15:02:23,491 [DEBUG] - --------------------------- 模拟交易 2025-05-28 15:02:23 ---------------------------
2025-05-28 15:02:23,514 [DEBUG] - 🔀 持仓周期权重聚合...
2025-05-28 15:02:24,588 [DEBUG] - 👌 权重聚合完成，耗时：1.076秒
2025-05-28 15:02:24,599 [DEBUG] - 🗓️ 回测模拟区间:2015-01-05~2025-05-27，选股结果区间:2015-01-05~2025-05-27
2025-05-28 15:02:24,804 [INFO] - 开始模拟日线交易...
2025-05-28 15:02:27,662 [DEBUG] - ℹ️ 实际模拟资金:100,000.00(整体使用率:100.00%), 印花税率:0.10%, 券商佣金费率:0.01%
2025-05-28 15:02:28,173 [DEBUG] - 🎯 开始模拟交易...
2025-05-28 15:02:35,605 [OK] - 完成模拟交易，花费时间: 7.433秒
2025-05-28 15:02:35,670 [DEBUG] - 📈 策略评价 --------------------------------
                                       0
累积净值                            6.25
年化收益                          19.27%
最大回撤                         -26.95%
最大回撤开始时间     2017-10-11 00:00:00
最大回撤结束时间     2018-10-29 00:00:00
年化收益/回撤比                     0.71
修复涨幅（均/最大）       5.26% / 36.90%
修复时间（均/最大）        106.1 / 650.6
盈利周期数                        1118.0
亏损周期数                        1406.0
胜率（含0/去0）          44.28% / 52.24%
每周期平均收益                     0.08%
盈亏收益比                          1.69
单周期最大盈利                     8.56%
单周期大亏损                      -6.57%
最大连续盈利周期数                   9.0
最大连续亏损周期数                  19.0
收益率标准差                       0.88%

📊 分年收益率 --------------------------------
             涨跌幅
交易日期           
2015-12-31  112.73%
2016-12-31   29.86%
2017-12-31   -1.35%
2018-12-31  -15.83%
2019-12-31   25.38%
2020-12-31   32.87%
2021-12-31    8.29%
2022-12-31    0.95%
2023-12-31    0.47%
2024-12-31   34.91%
2025-12-31   10.34%
2025-05-28 15:02:35,670 [DEBUG] - 💰 总手续费: ￥106,758.93

2025-05-28 15:02:35,670 [INFO] - 开始绘制资金曲线...
2025-05-28 15:08:44,971 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 15:08:44,991 [DEBUG] - # VERSION: select-stock-pro(1.4.2.timing)
2025-05-28 15:08:44,991 [DEBUG] - # BUILD VERSION: v1.4.2.timing.20250418
2025-05-28 15:08:44,991 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 15:08:45,017 [DEBUG] - --------------------------- 准备数据 2025-05-28 15:08:45 ---------------------------
2025-05-28 15:08:45,038 [DEBUG] - 🧹 删除运行缓存文件夹：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存
2025-05-28 15:08:45,489 [INFO] - 读取数据中心数据...
2025-05-28 15:08:45,489 [DEBUG] - 🛂 没有因子需要额外的全息字段
2025-05-28 15:08:45,489 [DEBUG] - 🗑️ [策略配置] 需要排除`科创板`
2025-05-28 15:08:45,489 [DEBUG] - 🕒 检测到需要分钟数据：['1030']，需要额外准备pivot数据
2025-05-28 15:08:45,502 [DEBUG] - 📂 读取到股票数量：5132，不包括科创板
2025-05-28 15:08:45,512 [DEBUG] - 🚀 多进程处理数据，进程数量：23
2025-05-28 15:09:28,213 [DEBUG] - 📈 保存股票预处理数据: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\股票预处理数据.pkl
2025-05-28 15:09:28,214 [DEBUG] - 📅 行情数据最新交易日期：2025-05-27 00:00:00
2025-05-28 15:09:32,926 [DEBUG] - 📄 生成行情数据透视表...
2025-05-28 15:09:32,926 [DEBUG] - ⚗️ 合成整体市场数据...
2025-05-28 15:09:35,500 [DEBUG] - [1/4] 开盘价透视表...
2025-05-28 15:09:38,422 [DEBUG] - [2/4] 收盘价透视表...
2025-05-28 15:09:41,334 [DEBUG] - [3/4] 前收盘价透视表...
2025-05-28 15:09:44,280 [DEBUG] - [4/4] 1030透视表...
2025-05-28 15:09:47,462 [DEBUG] - 🗄️ 保存行情数据透视表: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\全部股票行情pivot.pkl
2025-05-28 15:09:47,880 [OK] - 数据准备耗时：62.39 秒
2025-05-28 15:09:48,826 [DEBUG] - --------------------------- 因子计算 2025-05-28 15:09:48 ---------------------------
2025-05-28 15:09:48,847 [INFO] - 因子计算...
2025-05-28 15:09:48,847 [DEBUG] - 🛂 配置信息检查...
2025-05-28 15:09:48,847 [DEBUG] - ℹ️ 检测到没有财务因子
2025-05-28 15:09:48,847 [DEBUG] - 🔍 检测到外部数据：['5min_close']
2025-05-28 15:09:48,848 [DEBUG] - 💿 读取股票K线数据...
2025-05-28 15:09:53,782 [DEBUG] - * 总共计算因子个数：8 个
* 单次计算因子个数：32 个，(需分成1组计算)
* 需要计算币种数量：5077 个
2025-05-28 15:09:53,782 [DEBUG] - 🚀 多进程计算因子，进程数量：23
2025-05-28 15:09:53,782 [DEBUG] - 🗂️ 因子分片计算中，进度：1/1
2025-05-28 15:11:19,398 [DEBUG] - 📅 因子结果最晚日期：2025-05-27 00:00:00
2025-05-28 15:11:22,499 [DEBUG] - 💾 存储因子数据...
2025-05-28 15:11:22,499 [DEBUG] - - D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\all_factors_kline.pkl
2025-05-28 15:11:22,510 [DEBUG] - 最晚交易日期：2025-05-27 00:00:00
2025-05-28 15:11:24,937 [OK] - 因子计算完成，耗时：96.09秒
2025-05-28 15:11:26,514 [DEBUG] - --------------------------- 条件选股 2025-05-28 15:11:26 ---------------------------
2025-05-28 15:11:26,552 [DEBUG] - 🔍 因子文件：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\all_factors_kline.pkl
2025-05-28 15:11:28,166 [DEBUG] - 🎯 #0.KDJ_CMO熊市策略 选股启动...
2025-05-28 15:11:28,626 [DEBUG] - 📦 [#0.KDJ_CMO熊市策略] 选股数据加载完成，最晚日期：2025-05-27 00:00:00
2025-05-28 15:11:30,853 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 数据清洗完成，去掉空因子数据，最晚日期：2025-05-27 00:00:00
2025-05-28 15:11:31,837 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 前置筛选耗时：0.98s。数据最晚日期：2025-05-27 00:00:00
2025-05-28 15:11:39,712 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 选股复合因子计算耗时：7.87s。数据最晚日期：2025-05-27 00:00:00
2025-05-28 15:11:43,360 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 定风波择时：3.65s
2025-05-28 15:11:47,396 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 选股耗时：4.04s。数据最晚日期：2025-05-27 00:00:00
2025-05-28 15:11:47,421 [DEBUG] - 🏁 [#0.KDJ_CMO熊市策略] 选股耗时: 19.26s
2025-05-28 15:11:47,744 [DEBUG] - 🔍 计算`#0.KDJ_CMO熊市策略`最新选股结果, 数据最晚选股日：2025-05-27 00:00:00
2025-05-28 15:11:47,770 [DEBUG] - 💾 选股结果数据大小：1.9161 MB
2025-05-28 15:11:47,770 [OK] - 选股完成，总耗时：21.236秒
2025-05-28 15:11:47,770 [DEBUG] - --------------------------- 模拟交易 2025-05-28 15:11:47 ---------------------------
2025-05-28 15:11:47,792 [DEBUG] - 🔀 持仓周期权重聚合...
2025-05-28 15:11:48,837 [DEBUG] - 👌 权重聚合完成，耗时：1.046秒
2025-05-28 15:11:48,848 [DEBUG] - 🗓️ 回测模拟区间:2015-01-05~2025-05-27，选股结果区间:2015-01-05~2025-05-27
2025-05-28 15:11:49,047 [INFO] - 开始模拟日线交易...
2025-05-28 15:11:51,739 [DEBUG] - ℹ️ 实际模拟资金:100,000.00(整体使用率:100.00%), 印花税率:0.10%, 券商佣金费率:0.01%
2025-05-28 15:11:52,847 [DEBUG] - 🎯 开始模拟交易...
2025-05-28 15:12:04,189 [OK] - 完成模拟交易，花费时间: 11.341秒
2025-05-28 15:12:04,253 [DEBUG] - 📈 策略评价 --------------------------------
                                       0
累积净值                            6.25
年化收益                          19.27%
最大回撤                         -26.95%
最大回撤开始时间     2017-10-11 00:00:00
最大回撤结束时间     2018-10-29 00:00:00
年化收益/回撤比                     0.71
修复涨幅（均/最大）       5.26% / 36.90%
修复时间（均/最大）        106.1 / 650.6
盈利周期数                        1118.0
亏损周期数                        1406.0
胜率（含0/去0）          44.28% / 52.24%
每周期平均收益                     0.08%
盈亏收益比                          1.69
单周期最大盈利                     8.56%
单周期大亏损                      -6.57%
最大连续盈利周期数                   9.0
最大连续亏损周期数                  19.0
收益率标准差                       0.88%

📊 分年收益率 --------------------------------
             涨跌幅
交易日期           
2015-12-31  112.73%
2016-12-31   29.86%
2017-12-31   -1.35%
2018-12-31  -15.83%
2019-12-31   25.38%
2020-12-31   32.87%
2021-12-31    8.29%
2022-12-31    0.95%
2023-12-31    0.47%
2024-12-31   34.91%
2025-12-31   10.34%
2025-05-28 15:12:04,254 [DEBUG] - 💰 总手续费: ￥106,758.93

2025-05-28 15:12:04,254 [INFO] - 开始绘制资金曲线...
2025-05-28 15:39:33,198 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 15:39:33,220 [DEBUG] - # VERSION: select-stock-pro(1.4.2.timing)
2025-05-28 15:39:33,220 [DEBUG] - # BUILD VERSION: v1.4.2.timing.20250418
2025-05-28 15:39:33,220 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 15:39:33,253 [DEBUG] - --------------------------- 准备数据 2025-05-28 15:39:33 ---------------------------
2025-05-28 15:39:33,275 [DEBUG] - 🧹 删除运行缓存文件夹：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存
2025-05-28 15:39:34,119 [INFO] - 读取数据中心数据...
2025-05-28 15:39:34,119 [DEBUG] - 🛂 没有因子需要额外的全息字段
2025-05-28 15:39:34,119 [DEBUG] - 🗑️ [策略配置] 需要排除`科创板`
2025-05-28 15:39:34,119 [DEBUG] - 🕒 检测到需要分钟数据：['1000']，需要额外准备pivot数据
2025-05-28 15:39:34,133 [DEBUG] - 📂 读取到股票数量：5132，不包括科创板
2025-05-28 15:39:34,144 [DEBUG] - 🚀 多进程处理数据，进程数量：23
2025-05-28 15:40:16,414 [DEBUG] - 📈 保存股票预处理数据: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\股票预处理数据.pkl
2025-05-28 15:40:16,414 [DEBUG] - 📅 行情数据最新交易日期：2025-05-28 00:00:00
2025-05-28 15:40:20,384 [DEBUG] - 📄 生成行情数据透视表...
2025-05-28 15:40:20,385 [DEBUG] - ⚗️ 合成整体市场数据...
2025-05-28 15:40:22,827 [DEBUG] - [1/4] 开盘价透视表...
2025-05-28 15:40:26,423 [DEBUG] - [2/4] 收盘价透视表...
2025-05-28 15:40:29,652 [DEBUG] - [3/4] 前收盘价透视表...
2025-05-28 15:40:32,655 [DEBUG] - [4/4] 1000透视表...
2025-05-28 15:40:35,862 [DEBUG] - 🗄️ 保存行情数据透视表: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\全部股票行情pivot.pkl
2025-05-28 15:40:36,273 [OK] - 数据准备耗时：62.15 秒
2025-05-28 15:40:37,217 [DEBUG] - --------------------------- 因子计算 2025-05-28 15:40:37 ---------------------------
2025-05-28 15:40:37,237 [INFO] - 因子计算...
2025-05-28 15:40:37,237 [DEBUG] - 🛂 配置信息检查...
2025-05-28 15:40:37,237 [DEBUG] - ℹ️ 检测到没有财务因子
2025-05-28 15:40:37,237 [DEBUG] - 🔍 检测到外部数据：['5min_close']
2025-05-28 15:40:37,238 [DEBUG] - 💿 读取股票K线数据...
2025-05-28 15:40:42,178 [DEBUG] - * 总共计算因子个数：12 个
* 单次计算因子个数：32 个，(需分成1组计算)
* 需要计算币种数量：5077 个
2025-05-28 15:40:42,178 [DEBUG] - 🚀 多进程计算因子，进程数量：23
2025-05-28 15:40:42,179 [DEBUG] - 🗂️ 因子分片计算中，进度：1/1
2025-05-28 15:42:23,017 [DEBUG] - 📅 因子结果最晚日期：2025-05-27 00:00:00
2025-05-28 15:42:26,724 [DEBUG] - 💾 存储因子数据...
2025-05-28 15:42:26,725 [DEBUG] - - D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\all_factors_kline.pkl
2025-05-28 15:42:26,740 [DEBUG] - 最晚交易日期：2025-05-27 00:00:00
2025-05-28 15:42:31,253 [OK] - 因子计算完成，耗时：114.02秒
2025-05-28 15:42:35,626 [DEBUG] - --------------------------- 条件选股 2025-05-28 15:42:35 ---------------------------
2025-05-28 15:42:35,681 [DEBUG] - 🔍 因子文件：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\all_factors_kline.pkl
2025-05-28 15:42:38,018 [DEBUG] - 🎯 #0.KDJ_CMO增强策略_V2 选股启动...
2025-05-28 15:42:38,794 [DEBUG] - 📦 [#0.KDJ_CMO增强策略_V2] 选股数据加载完成，最晚日期：2025-05-27 00:00:00
2025-05-28 15:42:43,191 [DEBUG] - ➡️ [#0.KDJ_CMO增强策略_V2] 数据清洗完成，去掉空因子数据，最晚日期：2025-05-27 00:00:00
2025-05-28 15:42:51,244 [DEBUG] - ➡️ [#0.KDJ_CMO增强策略_V2] 前置筛选耗时：8.05s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 15:43:02,723 [DEBUG] - ➡️ [#0.KDJ_CMO增强策略_V2] 选股复合因子计算耗时：11.47s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 15:43:05,904 [DEBUG] - ➡️ [#0.KDJ_CMO增强策略_V2] 定风波择时：3.18s
2025-05-28 15:43:09,371 [DEBUG] - ➡️ [#0.KDJ_CMO增强策略_V2] 选股耗时：3.47s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 15:43:09,402 [DEBUG] - 🏁 [#0.KDJ_CMO增强策略_V2] 选股耗时: 31.38s
2025-05-28 15:43:09,798 [DEBUG] - 🔍 计算`#0.KDJ_CMO增强策略_V2`最新选股结果, 数据最晚选股日：2025-05-26 00:00:00
2025-05-28 15:43:09,847 [DEBUG] - 💾 选股结果数据大小：2.0777 MB
2025-05-28 15:43:09,847 [OK] - 选股完成，总耗时：34.200秒
2025-05-28 15:43:09,847 [DEBUG] - --------------------------- 模拟交易 2025-05-28 15:43:09 ---------------------------
2025-05-28 15:43:09,871 [DEBUG] - 🔀 持仓周期权重聚合...
2025-05-28 15:43:11,206 [DEBUG] - 👌 权重聚合完成，耗时：1.338秒
2025-05-28 15:43:11,217 [DEBUG] - 🗓️ 回测模拟区间:2015-01-05~2025-05-27，选股结果区间:2015-01-05~2025-05-26
2025-05-28 15:43:11,556 [INFO] - 开始模拟日线交易...
2025-05-28 15:43:15,190 [DEBUG] - ℹ️ 实际模拟资金:100,000.00(整体使用率:100.00%), 印花税率:0.10%, 券商佣金费率:0.01%
2025-05-28 15:43:15,829 [DEBUG] - 🎯 开始模拟交易...
2025-05-28 15:43:29,418 [OK] - 完成模拟交易，花费时间: 13.590秒
2025-05-28 15:43:29,486 [DEBUG] - 📈 策略评价 --------------------------------
                                       0
累积净值                            0.87
年化收益                          -1.31%
最大回撤                         -33.40%
最大回撤开始时间     2017-04-06 00:00:00
最大回撤结束时间     2024-09-06 00:00:00
年化收益/回撤比                    -0.04
修复涨幅（均/最大）      18.36% / 50.14%
修复时间（均/最大）   -4661.2 / -11239.8
盈利周期数                        1039.0
亏损周期数                        1485.0
胜率（含0/去0）          41.15% / 48.08%
每周期平均收益                    -0.00%
盈亏收益比                           1.4
单周期最大盈利                     6.52%
单周期大亏损                      -5.92%
最大连续盈利周期数                   9.0
最大连续亏损周期数                  32.0
收益率标准差                       0.58%

📊 分年收益率 --------------------------------
             涨跌幅
交易日期           
2015-12-31   11.25%
2016-12-31    1.47%
2017-12-31   -1.72%
2018-12-31  -18.51%
2019-12-31    3.63%
2020-12-31   -0.16%
2021-12-31    1.78%
2022-12-31    0.59%
2023-12-31    5.24%
2024-12-31  -11.25%
2025-12-31   -2.53%
2025-05-28 15:43:29,486 [DEBUG] - 💰 总手续费: ￥13,447.03

2025-05-28 15:43:29,486 [INFO] - 开始绘制资金曲线...
2025-05-28 15:44:45,448 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 15:44:45,470 [DEBUG] - # VERSION: select-stock-pro(1.4.2.timing)
2025-05-28 15:44:45,470 [DEBUG] - # BUILD VERSION: v1.4.2.timing.20250418
2025-05-28 15:44:45,470 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 15:44:45,501 [DEBUG] - --------------------------- 准备数据 2025-05-28 15:44:45 ---------------------------
2025-05-28 15:44:45,523 [DEBUG] - 🧹 删除运行缓存文件夹：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存
2025-05-28 15:44:46,105 [INFO] - 读取数据中心数据...
2025-05-28 15:44:46,105 [DEBUG] - 🛂 没有因子需要额外的全息字段
2025-05-28 15:44:46,105 [DEBUG] - 🗑️ [策略配置] 需要排除`科创板`
2025-05-28 15:44:46,105 [DEBUG] - 🕒 检测到需要分钟数据：['1015']，需要额外准备pivot数据
2025-05-28 15:44:46,120 [DEBUG] - 📂 读取到股票数量：5132，不包括科创板
2025-05-28 15:44:46,131 [DEBUG] - 🚀 多进程处理数据，进程数量：23
2025-05-28 15:46:53,031 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 15:46:53,052 [DEBUG] - # VERSION: select-stock-pro(1.4.2.timing)
2025-05-28 15:46:53,052 [DEBUG] - # BUILD VERSION: v1.4.2.timing.20250418
2025-05-28 15:46:53,052 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 15:46:53,079 [DEBUG] - --------------------------- 准备数据 2025-05-28 15:46:53 ---------------------------
2025-05-28 15:46:53,100 [DEBUG] - 🧹 删除运行缓存文件夹：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存
2025-05-28 15:46:53,101 [INFO] - 读取数据中心数据...
2025-05-28 15:46:53,101 [DEBUG] - 🛂 没有因子需要额外的全息字段
2025-05-28 15:46:53,101 [DEBUG] - 🗑️ [策略配置] 需要排除`科创板`
2025-05-28 15:46:53,101 [DEBUG] - 🕒 检测到需要分钟数据：['1015']，需要额外准备pivot数据
2025-05-28 15:46:53,115 [DEBUG] - 📂 读取到股票数量：5132，不包括科创板
2025-05-28 15:46:53,125 [DEBUG] - 🚀 多进程处理数据，进程数量：23
2025-05-28 15:47:34,457 [DEBUG] - 📈 保存股票预处理数据: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\股票预处理数据.pkl
2025-05-28 15:47:34,457 [DEBUG] - 📅 行情数据最新交易日期：2025-05-28 00:00:00
2025-05-28 15:47:38,553 [DEBUG] - 📄 生成行情数据透视表...
2025-05-28 15:47:38,554 [DEBUG] - ⚗️ 合成整体市场数据...
2025-05-28 15:47:41,035 [DEBUG] - [1/4] 开盘价透视表...
2025-05-28 15:47:43,971 [DEBUG] - [2/4] 收盘价透视表...
2025-05-28 15:47:46,963 [DEBUG] - [3/4] 前收盘价透视表...
2025-05-28 15:47:49,931 [DEBUG] - [4/4] 1015透视表...
2025-05-28 15:47:53,101 [DEBUG] - 🗄️ 保存行情数据透视表: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\全部股票行情pivot.pkl
2025-05-28 15:47:53,549 [OK] - 数据准备耗时：60.45 秒
2025-05-28 15:47:54,472 [DEBUG] - --------------------------- 因子计算 2025-05-28 15:47:54 ---------------------------
2025-05-28 15:47:54,492 [INFO] - 因子计算...
2025-05-28 15:47:54,492 [DEBUG] - 🛂 配置信息检查...
2025-05-28 15:47:54,492 [DEBUG] - ℹ️ 检测到财务因子：['B_total_equity_atoopc@xbx', 'R_np_atoopc@xbx_ttm', 'R_np_atoopc@xbx_单季']
2025-05-28 15:47:54,492 [DEBUG] - 🔍 检测到外部数据：['5min_close']
2025-05-28 15:47:54,494 [DEBUG] - 💿 读取股票K线数据...
2025-05-28 15:47:59,531 [DEBUG] - * 总共计算因子个数：13 个
* 单次计算因子个数：32 个，(需分成1组计算)
* 需要计算币种数量：5077 个
2025-05-28 15:47:59,531 [DEBUG] - 🚀 多进程计算因子，进程数量：23
2025-05-28 15:47:59,531 [DEBUG] - 🗂️ 因子分片计算中，进度：1/1
2025-05-28 15:49:47,448 [DEBUG] - 📅 因子结果最晚日期：2025-05-27 00:00:00
2025-05-28 15:49:50,012 [DEBUG] - 💾 存储因子数据...
2025-05-28 15:49:50,013 [DEBUG] - - D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\all_factors_kline.pkl
2025-05-28 15:49:50,022 [DEBUG] - 最晚交易日期：2025-05-27 00:00:00
2025-05-28 15:49:52,814 [OK] - 因子计算完成，耗时：118.32秒
2025-05-28 15:49:54,259 [DEBUG] - --------------------------- 条件选股 2025-05-28 15:49:54 ---------------------------
2025-05-28 15:52:48,504 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 15:52:48,525 [DEBUG] - # VERSION: select-stock-pro(1.4.2.timing)
2025-05-28 15:52:48,525 [DEBUG] - # BUILD VERSION: v1.4.2.timing.20250418
2025-05-28 15:52:48,525 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 15:52:48,551 [DEBUG] - --------------------------- 准备数据 2025-05-28 15:52:48 ---------------------------
2025-05-28 15:52:48,573 [DEBUG] - 🧹 删除运行缓存文件夹：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存
2025-05-28 15:52:49,175 [INFO] - 读取数据中心数据...
2025-05-28 15:52:49,175 [DEBUG] - 🛂 没有因子需要额外的全息字段
2025-05-28 15:52:49,175 [DEBUG] - 🗑️ [策略配置] 需要排除`科创板`
2025-05-28 15:52:49,175 [DEBUG] - 🕒 检测到需要分钟数据：['1030']，需要额外准备pivot数据
2025-05-28 15:52:49,189 [DEBUG] - 📂 读取到股票数量：5132，不包括科创板
2025-05-28 15:52:49,199 [DEBUG] - 🚀 多进程处理数据，进程数量：23
2025-05-28 15:53:30,869 [DEBUG] - 📈 保存股票预处理数据: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\股票预处理数据.pkl
2025-05-28 15:53:30,870 [DEBUG] - 📅 行情数据最新交易日期：2025-05-28 00:00:00
2025-05-28 15:53:34,658 [DEBUG] - 📄 生成行情数据透视表...
2025-05-28 15:53:34,658 [DEBUG] - ⚗️ 合成整体市场数据...
2025-05-28 15:53:37,494 [DEBUG] - [1/4] 开盘价透视表...
2025-05-28 15:53:40,388 [DEBUG] - [2/4] 收盘价透视表...
2025-05-28 15:53:43,327 [DEBUG] - [3/4] 前收盘价透视表...
2025-05-28 15:53:46,204 [DEBUG] - [4/4] 1030透视表...
2025-05-28 15:53:49,386 [DEBUG] - 🗄️ 保存行情数据透视表: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\全部股票行情pivot.pkl
2025-05-28 15:53:49,835 [OK] - 数据准备耗时：60.66 秒
2025-05-28 15:53:50,858 [DEBUG] - --------------------------- 因子计算 2025-05-28 15:53:50 ---------------------------
2025-05-28 15:53:50,878 [INFO] - 因子计算...
2025-05-28 15:53:50,878 [DEBUG] - 🛂 配置信息检查...
2025-05-28 15:53:50,878 [DEBUG] - ℹ️ 检测到没有财务因子
2025-05-28 15:53:50,878 [DEBUG] - 🔍 检测到外部数据：['5min_close']
2025-05-28 15:53:50,880 [DEBUG] - 💿 读取股票K线数据...
2025-05-28 15:53:55,551 [DEBUG] - * 总共计算因子个数：8 个
* 单次计算因子个数：32 个，(需分成1组计算)
* 需要计算币种数量：5077 个
2025-05-28 15:53:55,551 [DEBUG] - 🚀 多进程计算因子，进程数量：23
2025-05-28 15:53:55,551 [DEBUG] - 🗂️ 因子分片计算中，进度：1/1
2025-05-28 15:55:19,919 [DEBUG] - 📅 因子结果最晚日期：2025-05-27 00:00:00
2025-05-28 15:55:22,169 [DEBUG] - 💾 存储因子数据...
2025-05-28 15:55:22,169 [DEBUG] - - D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\all_factors_kline.pkl
2025-05-28 15:55:22,179 [DEBUG] - 最晚交易日期：2025-05-27 00:00:00
2025-05-28 15:55:24,330 [OK] - 因子计算完成，耗时：93.45秒
2025-05-28 15:55:25,713 [DEBUG] - --------------------------- 条件选股 2025-05-28 15:55:25 ---------------------------
2025-05-28 15:55:25,776 [DEBUG] - 🔍 因子文件：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\all_factors_kline.pkl
2025-05-28 15:55:27,381 [DEBUG] - 🎯 #0.KDJ_CMO熊市策略 选股启动...
2025-05-28 15:55:27,878 [DEBUG] - 📦 [#0.KDJ_CMO熊市策略] 选股数据加载完成，最晚日期：2025-05-27 00:00:00
2025-05-28 15:55:29,887 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 数据清洗完成，去掉空因子数据，最晚日期：2025-05-27 00:00:00
2025-05-28 15:55:30,778 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 前置筛选耗时：0.88s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 15:55:38,515 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 选股复合因子计算耗时：7.73s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 15:55:41,922 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 定风波择时：3.41s
2025-05-28 15:55:45,782 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 选股耗时：3.86s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 15:55:45,805 [DEBUG] - 🏁 [#0.KDJ_CMO熊市策略] 选股耗时: 18.42s
2025-05-28 15:55:46,094 [DEBUG] - 🔍 计算`#0.KDJ_CMO熊市策略`最新选股结果, 数据最晚选股日：2025-05-26 00:00:00
2025-05-28 15:55:46,119 [DEBUG] - 💾 选股结果数据大小：1.9287 MB
2025-05-28 15:55:46,119 [OK] - 选股完成，总耗时：20.385秒
2025-05-28 15:55:46,119 [DEBUG] - --------------------------- 模拟交易 2025-05-28 15:55:46 ---------------------------
2025-05-28 15:55:46,143 [DEBUG] - 🔀 持仓周期权重聚合...
2025-05-28 15:55:47,164 [DEBUG] - 👌 权重聚合完成，耗时：1.024秒
2025-05-28 15:55:47,173 [DEBUG] - 🗓️ 回测模拟区间:2015-01-05~2025-05-27，选股结果区间:2015-01-05~2025-05-26
2025-05-28 15:55:47,380 [INFO] - 开始模拟日线交易...
2025-05-28 15:55:50,065 [DEBUG] - ℹ️ 实际模拟资金:100,000.00(整体使用率:100.00%), 印花税率:0.10%, 券商佣金费率:0.01%
2025-05-28 15:55:50,574 [DEBUG] - 🎯 开始模拟交易...
2025-05-28 15:55:57,974 [OK] - 完成模拟交易，花费时间: 7.401秒
2025-05-28 15:55:58,060 [DEBUG] - 📈 策略评价 --------------------------------
                                       0
累积净值                            6.25
年化收益                          19.27%
最大回撤                         -26.95%
最大回撤开始时间     2017-10-11 00:00:00
最大回撤结束时间     2018-10-29 00:00:00
年化收益/回撤比                     0.71
修复涨幅（均/最大）       5.26% / 36.90%
修复时间（均/最大）        106.1 / 650.6
盈利周期数                        1118.0
亏损周期数                        1406.0
胜率（含0/去0）          44.28% / 52.24%
每周期平均收益                     0.08%
盈亏收益比                          1.69
单周期最大盈利                     8.56%
单周期大亏损                      -6.57%
最大连续盈利周期数                   9.0
最大连续亏损周期数                  19.0
收益率标准差                       0.88%

📊 分年收益率 --------------------------------
             涨跌幅
交易日期           
2015-12-31  112.73%
2016-12-31   29.86%
2017-12-31   -1.35%
2018-12-31  -15.83%
2019-12-31   25.38%
2020-12-31   32.87%
2021-12-31    8.29%
2022-12-31    0.95%
2023-12-31    0.47%
2024-12-31   34.91%
2025-12-31   10.34%
2025-05-28 15:55:58,061 [DEBUG] - 💰 总手续费: ￥106,758.93

2025-05-28 15:55:58,061 [INFO] - 开始绘制资金曲线...
2025-05-28 15:58:16,236 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 15:58:16,257 [DEBUG] - # VERSION: select-stock-pro(1.4.2.timing)
2025-05-28 15:58:16,257 [DEBUG] - # BUILD VERSION: v1.4.2.timing.20250418
2025-05-28 15:58:16,257 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 15:58:16,282 [DEBUG] - --------------------------- 准备数据 2025-05-28 15:58:16 ---------------------------
2025-05-28 15:58:16,303 [DEBUG] - 🧹 删除运行缓存文件夹：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存
2025-05-28 15:58:17,330 [INFO] - 读取数据中心数据...
2025-05-28 15:58:17,331 [DEBUG] - 🛂 没有因子需要额外的全息字段
2025-05-28 15:58:17,331 [DEBUG] - 🗑️ [策略配置] 需要排除`科创板`
2025-05-28 15:58:17,331 [DEBUG] - 🕒 检测到需要分钟数据：['1030']，需要额外准备pivot数据
2025-05-28 15:58:17,345 [DEBUG] - 📂 读取到股票数量：5132，不包括科创板
2025-05-28 15:58:17,356 [DEBUG] - 🚀 多进程处理数据，进程数量：23
2025-05-28 15:58:57,228 [DEBUG] - 📈 保存股票预处理数据: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\股票预处理数据.pkl
2025-05-28 15:58:57,228 [DEBUG] - 📅 行情数据最新交易日期：2025-05-28 00:00:00
2025-05-28 15:59:01,274 [DEBUG] - 📄 生成行情数据透视表...
2025-05-28 15:59:01,274 [DEBUG] - ⚗️ 合成整体市场数据...
2025-05-28 15:59:03,762 [DEBUG] - [1/4] 开盘价透视表...
2025-05-28 15:59:06,755 [DEBUG] - [2/4] 收盘价透视表...
2025-05-28 15:59:09,755 [DEBUG] - [3/4] 前收盘价透视表...
2025-05-28 15:59:12,750 [DEBUG] - [4/4] 1030透视表...
2025-05-28 15:59:15,996 [DEBUG] - 🗄️ 保存行情数据透视表: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\全部股票行情pivot.pkl
2025-05-28 15:59:16,946 [OK] - 数据准备耗时：59.61 秒
2025-05-28 15:59:17,943 [DEBUG] - --------------------------- 因子计算 2025-05-28 15:59:17 ---------------------------
2025-05-28 15:59:17,964 [INFO] - 因子计算...
2025-05-28 15:59:17,964 [DEBUG] - 🛂 配置信息检查...
2025-05-28 15:59:17,964 [DEBUG] - ℹ️ 检测到没有财务因子
2025-05-28 15:59:17,964 [DEBUG] - 🔍 检测到外部数据：['15min_close']
2025-05-28 15:59:17,966 [DEBUG] - 💿 读取股票K线数据...
2025-05-28 15:59:22,915 [DEBUG] - * 总共计算因子个数：4 个
* 单次计算因子个数：32 个，(需分成1组计算)
* 需要计算币种数量：5077 个
2025-05-28 15:59:22,916 [DEBUG] - 🚀 多进程计算因子，进程数量：23
2025-05-28 15:59:22,916 [DEBUG] - 🗂️ 因子分片计算中，进度：1/1
2025-05-28 16:00:43,344 [DEBUG] - 📅 因子结果最晚日期：2025-05-27 00:00:00
2025-05-28 16:00:45,404 [DEBUG] - 💾 存储因子数据...
2025-05-28 16:00:45,404 [DEBUG] - - D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\all_factors_kline.pkl
2025-05-28 16:00:45,414 [DEBUG] - 最晚交易日期：2025-05-27 00:00:00
2025-05-28 16:00:47,815 [OK] - 因子计算完成，耗时：89.85秒
2025-05-28 16:00:51,242 [DEBUG] - --------------------------- 条件选股 2025-05-28 16:00:51 ---------------------------
2025-05-28 16:00:51,289 [DEBUG] - 🔍 因子文件：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\all_factors_kline.pkl
2025-05-28 16:00:55,939 [DEBUG] - 🎯 #0.KDJ_CMO熊市策略 选股启动...
2025-05-28 16:00:56,691 [DEBUG] - 📦 [#0.KDJ_CMO熊市策略] 选股数据加载完成，最晚日期：2025-05-27 00:00:00
2025-05-28 16:00:59,492 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 数据清洗完成，去掉空因子数据，最晚日期：2025-05-27 00:00:00
2025-05-28 16:01:00,504 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 前置筛选耗时：1.00s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 16:01:01,221 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 选股复合因子计算耗时：0.71s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 16:01:01,250 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 定风波择时：0.03s
2025-05-28 16:01:02,850 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 选股耗时：1.60s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 16:01:02,904 [DEBUG] - 🏁 [#0.KDJ_CMO熊市策略] 选股耗时: 6.97s
2025-05-28 16:01:03,300 [DEBUG] - 🔍 计算`#0.KDJ_CMO熊市策略`最新选股结果, 数据最晚选股日：2025-05-26 00:00:00
2025-05-28 16:01:03,535 [DEBUG] - 💾 选股结果数据大小：3.7909 MB
2025-05-28 16:01:03,536 [OK] - 选股完成，总耗时：12.273秒
2025-05-28 16:01:03,536 [DEBUG] - --------------------------- 模拟交易 2025-05-28 16:01:03 ---------------------------
2025-05-28 16:01:03,561 [DEBUG] - 🔀 持仓周期权重聚合...
2025-05-28 16:01:04,939 [DEBUG] - 👌 权重聚合完成，耗时：1.382秒
2025-05-28 16:01:04,951 [DEBUG] - 🗓️ 回测模拟区间:2015-01-05~2025-05-27，选股结果区间:2015-01-05~2025-05-26
2025-05-28 16:01:05,333 [INFO] - 开始模拟日线交易...
2025-05-28 16:01:09,125 [DEBUG] - ℹ️ 实际模拟资金:100,000.00(整体使用率:100.00%), 印花税率:0.10%, 券商佣金费率:0.01%
2025-05-28 16:01:09,757 [DEBUG] - 🎯 开始模拟交易...
2025-05-28 16:01:25,242 [OK] - 完成模拟交易，花费时间: 15.486秒
2025-05-28 16:01:25,324 [DEBUG] - 📈 策略评价 --------------------------------
                                       0
累积净值                            0.03
年化收益                         -28.32%
最大回撤                         -97.90%
最大回撤开始时间     2015-06-11 00:00:00
最大回撤结束时间     2024-09-18 00:00:00
年化收益/回撤比                    -0.29
修复涨幅（均/最大）  2201.34% / 4667.10%
修复时间（均/最大）    -3438.1 / -4236.4
盈利周期数                         866.0
亏损周期数                        1658.0
胜率（含0/去0）          34.30% / 42.53%
每周期平均收益                    -0.13%
盈亏收益比                          1.33
单周期最大盈利                     7.77%
单周期大亏损                      -9.37%
最大连续盈利周期数                   7.0
最大连续亏损周期数                  40.0
收益率标准差                       1.23%

📊 分年收益率 --------------------------------
             涨跌幅
交易日期           
2015-12-31  -25.91%
2016-12-31  -47.93%
2017-12-31  -35.91%
2018-12-31  -57.94%
2019-12-31  -38.91%
2020-12-31  -20.34%
2021-12-31  -11.76%
2022-12-31  -20.44%
2023-12-31   -4.34%
2024-12-31   -1.87%
2025-12-31   -5.88%
2025-05-28 16:01:25,325 [DEBUG] - 💰 总手续费: ￥15,367.25

2025-05-28 16:01:25,325 [INFO] - 开始绘制资金曲线...
2025-05-28 16:11:06,624 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 16:11:06,645 [DEBUG] - # VERSION: select-stock-pro(1.4.2.timing)
2025-05-28 16:11:06,645 [DEBUG] - # BUILD VERSION: v1.4.2.timing.20250418
2025-05-28 16:11:06,645 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 16:11:06,671 [DEBUG] - --------------------------- 准备数据 2025-05-28 16:11:06 ---------------------------
2025-05-28 16:11:06,693 [DEBUG] - 🧹 删除运行缓存文件夹：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存
2025-05-28 16:11:07,245 [INFO] - 读取数据中心数据...
2025-05-28 16:11:07,245 [DEBUG] - 🛂 没有因子需要额外的全息字段
2025-05-28 16:11:07,245 [DEBUG] - 🗑️ [策略配置] 需要排除`科创板`
2025-05-28 16:11:07,245 [DEBUG] - 🕒 检测到需要分钟数据：['1030']，需要额外准备pivot数据
2025-05-28 16:11:07,271 [DEBUG] - 📂 读取到股票数量：5132，不包括科创板
2025-05-28 16:11:07,283 [DEBUG] - 🚀 多进程处理数据，进程数量：23
2025-05-28 16:11:44,117 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 16:11:44,138 [DEBUG] - # VERSION: select-stock-pro(1.4.2.timing)
2025-05-28 16:11:44,138 [DEBUG] - # BUILD VERSION: v1.4.2.timing.20250418
2025-05-28 16:11:44,138 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 16:11:44,167 [DEBUG] - --------------------------- 准备数据 2025-05-28 16:11:44 ---------------------------
2025-05-28 16:11:44,189 [DEBUG] - 🧹 删除运行缓存文件夹：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存
2025-05-28 16:11:44,190 [INFO] - 读取数据中心数据...
2025-05-28 16:11:44,190 [DEBUG] - 🛂 没有因子需要额外的全息字段
2025-05-28 16:11:44,190 [DEBUG] - 🗑️ [策略配置] 需要排除`科创板`
2025-05-28 16:11:44,190 [DEBUG] - 🕒 检测到需要分钟数据：['1030']，需要额外准备pivot数据
2025-05-28 16:11:44,222 [DEBUG] - 📂 读取到股票数量：5132，不包括科创板
2025-05-28 16:11:44,234 [DEBUG] - 🚀 多进程处理数据，进程数量：23
2025-05-28 16:13:04,787 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 16:13:04,808 [DEBUG] - # VERSION: select-stock-pro(1.4.2.timing)
2025-05-28 16:13:04,808 [DEBUG] - # BUILD VERSION: v1.4.2.timing.20250418
2025-05-28 16:13:04,808 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 16:13:04,834 [DEBUG] - --------------------------- 准备数据 2025-05-28 16:13:04 ---------------------------
2025-05-28 16:13:04,856 [DEBUG] - 🧹 删除运行缓存文件夹：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存
2025-05-28 16:13:04,857 [INFO] - 读取数据中心数据...
2025-05-28 16:13:04,857 [DEBUG] - 🛂 没有因子需要额外的全息字段
2025-05-28 16:13:04,857 [DEBUG] - 🗑️ [策略配置] 需要排除`科创板`
2025-05-28 16:13:04,857 [DEBUG] - 🕒 检测到需要分钟数据：['1030']，需要额外准备pivot数据
2025-05-28 16:13:04,888 [DEBUG] - 📂 读取到股票数量：5132，不包括科创板
2025-05-28 16:13:04,898 [DEBUG] - 🚀 多进程处理数据，进程数量：23
2025-05-28 16:15:12,408 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 16:15:12,429 [DEBUG] - # VERSION: select-stock-pro(1.4.2.timing)
2025-05-28 16:15:12,429 [DEBUG] - # BUILD VERSION: v1.4.2.timing.20250418
2025-05-28 16:15:12,429 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 16:15:12,457 [DEBUG] - --------------------------- 准备数据 2025-05-28 16:15:12 ---------------------------
2025-05-28 16:15:12,478 [DEBUG] - 🧹 删除运行缓存文件夹：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存
2025-05-28 16:15:12,479 [INFO] - 读取数据中心数据...
2025-05-28 16:15:12,479 [DEBUG] - 🛂 没有因子需要额外的全息字段
2025-05-28 16:15:12,479 [DEBUG] - 🗑️ [策略配置] 需要排除`科创板`
2025-05-28 16:15:12,479 [DEBUG] - 🕒 检测到需要分钟数据：['1030']，需要额外准备pivot数据
2025-05-28 16:15:12,515 [DEBUG] - 📂 读取到股票数量：5132，不包括科创板
2025-05-28 16:15:12,530 [DEBUG] - 🚀 多进程处理数据，进程数量：23
2025-05-28 16:15:53,417 [DEBUG] - 📈 保存股票预处理数据: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\股票预处理数据.pkl
2025-05-28 16:15:53,418 [DEBUG] - 📅 行情数据最新交易日期：2025-05-28 00:00:00
2025-05-28 16:15:57,600 [DEBUG] - 📄 生成行情数据透视表...
2025-05-28 16:15:57,600 [DEBUG] - ⚗️ 合成整体市场数据...
2025-05-28 16:16:00,114 [DEBUG] - [1/4] 开盘价透视表...
2025-05-28 16:16:03,418 [DEBUG] - [2/4] 收盘价透视表...
2025-05-28 16:16:07,070 [DEBUG] - [3/4] 前收盘价透视表...
2025-05-28 16:16:10,537 [DEBUG] - [4/4] 1030透视表...
2025-05-28 16:16:13,968 [DEBUG] - 🗄️ 保存行情数据透视表: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\全部股票行情pivot.pkl
2025-05-28 16:16:14,488 [OK] - 数据准备耗时：62.01 秒
2025-05-28 16:16:15,517 [DEBUG] - --------------------------- 因子计算 2025-05-28 16:16:15 ---------------------------
2025-05-28 16:16:15,539 [INFO] - 因子计算...
2025-05-28 16:16:15,539 [DEBUG] - 🛂 配置信息检查...
2025-05-28 16:16:15,539 [DEBUG] - ℹ️ 检测到没有财务因子
2025-05-28 16:16:15,539 [DEBUG] - 🔍 检测到外部数据：['15min_close']
2025-05-28 16:16:15,541 [DEBUG] - 💿 读取股票K线数据...
2025-05-28 16:16:21,111 [DEBUG] - * 总共计算因子个数：6 个
* 单次计算因子个数：32 个，(需分成1组计算)
* 需要计算币种数量：5077 个
2025-05-28 16:16:21,111 [DEBUG] - 🚀 多进程计算因子，进程数量：23
2025-05-28 16:16:21,111 [DEBUG] - 🗂️ 因子分片计算中，进度：1/1
2025-05-28 16:17:42,140 [DEBUG] - 📅 因子结果最晚日期：2025-05-27 00:00:00
2025-05-28 16:17:44,307 [DEBUG] - 💾 存储因子数据...
2025-05-28 16:17:44,308 [DEBUG] - - D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\all_factors_kline.pkl
2025-05-28 16:17:44,317 [DEBUG] - 最晚交易日期：2025-05-27 00:00:00
2025-05-28 16:17:46,371 [OK] - 因子计算完成，耗时：90.83秒
2025-05-28 16:17:47,684 [DEBUG] - --------------------------- 条件选股 2025-05-28 16:17:47 ---------------------------
2025-05-28 16:17:47,722 [DEBUG] - 🔍 因子文件：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\all_factors_kline.pkl
2025-05-28 16:17:49,323 [DEBUG] - 🎯 #0.KDJ_CMO熊市策略 选股启动...
2025-05-28 16:17:49,762 [DEBUG] - 📦 [#0.KDJ_CMO熊市策略] 选股数据加载完成，最晚日期：2025-05-27 00:00:00
2025-05-28 16:17:51,591 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 数据清洗完成，去掉空因子数据，最晚日期：2025-05-27 00:00:00
2025-05-28 16:17:52,456 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 前置筛选耗时：0.86s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 16:18:00,098 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 选股复合因子计算耗时：7.64s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 16:18:00,123 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 定风波择时：0.02s
2025-05-28 16:18:03,982 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 选股耗时：3.86s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 16:18:04,007 [DEBUG] - 🏁 [#0.KDJ_CMO熊市策略] 选股耗时: 14.68s
2025-05-28 16:18:04,291 [DEBUG] - 🔍 计算`#0.KDJ_CMO熊市策略`最新选股结果, 数据最晚选股日：2025-05-26 00:00:00
2025-05-28 16:18:04,320 [DEBUG] - 💾 选股结果数据大小：1.9292 MB
2025-05-28 16:18:04,320 [OK] - 选股完成，总耗时：16.615秒
2025-05-28 16:18:04,320 [DEBUG] - --------------------------- 模拟交易 2025-05-28 16:18:04 ---------------------------
2025-05-28 16:18:04,343 [DEBUG] - 🔀 持仓周期权重聚合...
2025-05-28 16:18:05,393 [DEBUG] - 👌 权重聚合完成，耗时：1.052秒
2025-05-28 16:18:05,403 [DEBUG] - 🗓️ 回测模拟区间:2015-01-05~2025-05-27，选股结果区间:2015-01-05~2025-05-26
2025-05-28 16:18:05,664 [INFO] - 开始模拟日线交易...
2025-05-28 16:18:08,392 [DEBUG] - ℹ️ 实际模拟资金:100,000.00(整体使用率:100.00%), 印花税率:0.10%, 券商佣金费率:0.01%
2025-05-28 16:18:08,956 [DEBUG] - 🎯 开始模拟交易...
2025-05-28 16:18:16,366 [OK] - 完成模拟交易，花费时间: 7.410秒
2025-05-28 16:18:16,463 [DEBUG] - 📈 策略评价 --------------------------------
                                       0
累积净值                            2.12
年化收益                           7.52%
最大回撤                         -71.22%
最大回撤开始时间     2016-11-22 00:00:00
最大回撤结束时间     2019-01-30 00:00:00
年化收益/回撤比                     0.11
修复涨幅（均/最大）    100.48% / 247.44%
修复时间（均/最大）      3503.1 / 6272.7
盈利周期数                        1351.0
亏损周期数                        1173.0
胜率（含0/去0）          53.50% / 53.50%
每周期平均收益                     0.05%
盈亏收益比                          0.94
单周期最大盈利                    11.20%
单周期大亏损                      -9.85%
最大连续盈利周期数                  14.0
最大连续亏损周期数                  12.0
收益率标准差                       1.94%

📊 分年收益率 --------------------------------
             涨跌幅
交易日期           
2015-12-31  227.02%
2016-12-31    2.15%
2017-12-31  -24.35%
2018-12-31  -57.96%
2019-12-31   13.19%
2020-12-31   18.95%
2021-12-31   29.67%
2022-12-31   10.11%
2023-12-31   -3.18%
2024-12-31   -8.23%
2025-12-31   17.06%
2025-05-28 16:18:16,464 [DEBUG] - 💰 总手续费: ￥108,418.47

2025-05-28 16:18:16,464 [INFO] - 开始绘制资金曲线...
2025-05-28 16:20:02,762 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 16:20:02,784 [DEBUG] - # VERSION: select-stock-pro(1.4.2.timing)
2025-05-28 16:20:02,784 [DEBUG] - # BUILD VERSION: v1.4.2.timing.20250418
2025-05-28 16:20:02,784 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 16:20:02,812 [DEBUG] - --------------------------- 准备数据 2025-05-28 16:20:02 ---------------------------
2025-05-28 16:20:02,834 [DEBUG] - 🧹 删除运行缓存文件夹：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存
2025-05-28 16:20:03,283 [INFO] - 读取数据中心数据...
2025-05-28 16:20:03,284 [DEBUG] - 🛂 没有因子需要额外的全息字段
2025-05-28 16:20:03,284 [DEBUG] - 🗑️ [策略配置] 需要排除`科创板`
2025-05-28 16:20:03,284 [DEBUG] - 🕒 检测到需要分钟数据：['1030']，需要额外准备pivot数据
2025-05-28 16:20:03,297 [DEBUG] - 📂 读取到股票数量：5132，不包括科创板
2025-05-28 16:20:03,307 [DEBUG] - 🚀 多进程处理数据，进程数量：23
2025-05-28 16:20:45,032 [DEBUG] - 📈 保存股票预处理数据: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\股票预处理数据.pkl
2025-05-28 16:20:45,032 [DEBUG] - 📅 行情数据最新交易日期：2025-05-28 00:00:00
2025-05-28 16:20:48,780 [DEBUG] - 📄 生成行情数据透视表...
2025-05-28 16:20:48,780 [DEBUG] - ⚗️ 合成整体市场数据...
2025-05-28 16:20:51,637 [DEBUG] - [1/4] 开盘价透视表...
2025-05-28 16:20:54,605 [DEBUG] - [2/4] 收盘价透视表...
2025-05-28 16:20:57,636 [DEBUG] - [3/4] 前收盘价透视表...
2025-05-28 16:21:00,676 [DEBUG] - [4/4] 1030透视表...
2025-05-28 16:21:03,894 [DEBUG] - 🗄️ 保存行情数据透视表: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\全部股票行情pivot.pkl
2025-05-28 16:21:04,310 [OK] - 数据准备耗时：61.03 秒
2025-05-28 16:21:05,324 [DEBUG] - --------------------------- 因子计算 2025-05-28 16:21:05 ---------------------------
2025-05-28 16:21:05,345 [INFO] - 因子计算...
2025-05-28 16:21:05,345 [DEBUG] - 🛂 配置信息检查...
2025-05-28 16:21:05,345 [DEBUG] - ℹ️ 检测到没有财务因子
2025-05-28 16:21:05,345 [DEBUG] - 🔍 检测到外部数据：['5min_close']
2025-05-28 16:21:05,346 [DEBUG] - 💿 读取股票K线数据...
2025-05-28 16:21:09,980 [DEBUG] - * 总共计算因子个数：8 个
* 单次计算因子个数：32 个，(需分成1组计算)
* 需要计算币种数量：5077 个
2025-05-28 16:21:09,980 [DEBUG] - 🚀 多进程计算因子，进程数量：23
2025-05-28 16:21:09,980 [DEBUG] - 🗂️ 因子分片计算中，进度：1/1
2025-05-28 16:22:33,808 [DEBUG] - 📅 因子结果最晚日期：2025-05-27 00:00:00
2025-05-28 16:22:36,183 [DEBUG] - 💾 存储因子数据...
2025-05-28 16:22:36,183 [DEBUG] - - D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\all_factors_kline.pkl
2025-05-28 16:22:36,192 [DEBUG] - 最晚交易日期：2025-05-27 00:00:00
2025-05-28 16:22:38,319 [OK] - 因子计算完成，耗时：92.97秒
2025-05-28 16:22:39,633 [DEBUG] - --------------------------- 条件选股 2025-05-28 16:22:39 ---------------------------
2025-05-28 16:22:39,669 [DEBUG] - 🔍 因子文件：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\all_factors_kline.pkl
2025-05-28 16:22:41,270 [DEBUG] - 🎯 #0.KDJ_CMO熊市策略 选股启动...
2025-05-28 16:22:41,777 [DEBUG] - 📦 [#0.KDJ_CMO熊市策略] 选股数据加载完成，最晚日期：2025-05-27 00:00:00
2025-05-28 16:22:43,788 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 数据清洗完成，去掉空因子数据，最晚日期：2025-05-27 00:00:00
2025-05-28 16:22:44,723 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 前置筛选耗时：0.93s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 16:22:52,390 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 选股复合因子计算耗时：7.66s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 16:22:55,796 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 定风波择时：3.41s
2025-05-28 16:22:59,632 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 选股耗时：3.84s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 16:22:59,656 [DEBUG] - 🏁 [#0.KDJ_CMO熊市策略] 选股耗时: 18.39s
2025-05-28 16:22:59,940 [DEBUG] - 🔍 计算`#0.KDJ_CMO熊市策略`最新选股结果, 数据最晚选股日：2025-05-26 00:00:00
2025-05-28 16:22:59,964 [DEBUG] - 💾 选股结果数据大小：1.9291 MB
2025-05-28 16:22:59,964 [OK] - 选股完成，总耗时：20.310秒
2025-05-28 16:22:59,964 [DEBUG] - --------------------------- 模拟交易 2025-05-28 16:22:59 ---------------------------
2025-05-28 16:22:59,987 [DEBUG] - 🔀 持仓周期权重聚合...
2025-05-28 16:23:01,061 [DEBUG] - 👌 权重聚合完成，耗时：1.076秒
2025-05-28 16:23:01,071 [DEBUG] - 🗓️ 回测模拟区间:2015-01-05~2025-05-27，选股结果区间:2015-01-05~2025-05-26
2025-05-28 16:23:01,284 [INFO] - 开始模拟日线交易...
2025-05-28 16:23:03,942 [DEBUG] - ℹ️ 实际模拟资金:100,000.00(整体使用率:100.00%), 印花税率:0.10%, 券商佣金费率:0.01%
2025-05-28 16:23:04,440 [DEBUG] - 🎯 开始模拟交易...
2025-05-28 16:23:11,687 [OK] - 完成模拟交易，花费时间: 7.247秒
2025-05-28 16:23:11,751 [DEBUG] - 📈 策略评价 --------------------------------
                                       0
累积净值                            6.79
年化收益                          20.23%
最大回撤                         -24.29%
最大回撤开始时间     2018-02-26 00:00:00
最大回撤结束时间     2018-10-29 00:00:00
年化收益/回撤比                     0.83
修复涨幅（均/最大）       4.75% / 32.09%
修复时间（均/最大）         91.9 / 551.3
盈利周期数                        1121.0
亏损周期数                        1403.0
胜率（含0/去0）          44.40% / 52.60%
每周期平均收益                     0.08%
盈亏收益比                          1.73
单周期最大盈利                     8.60%
单周期大亏损                      -6.15%
最大连续盈利周期数                   9.0
最大连续亏损周期数                  16.0
收益率标准差                       0.85%

📊 分年收益率 --------------------------------
             涨跌幅
交易日期           
2015-12-31   101.1%
2016-12-31   27.88%
2017-12-31    4.08%
2018-12-31  -15.92%
2019-12-31   22.09%
2020-12-31   30.61%
2021-12-31   10.02%
2022-12-31   13.12%
2023-12-31   -2.59%
2024-12-31   43.82%
2025-12-31    8.58%
2025-05-28 16:23:11,752 [DEBUG] - 💰 总手续费: ￥104,167.91

2025-05-28 16:23:11,752 [INFO] - 开始绘制资金曲线...
2025-05-28 16:24:14,986 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 16:24:15,007 [DEBUG] - # VERSION: select-stock-pro(1.4.2.timing)
2025-05-28 16:24:15,007 [DEBUG] - # BUILD VERSION: v1.4.2.timing.20250418
2025-05-28 16:24:15,008 [DEBUG] - ################################# [SYSTEM INFO] ##################################
2025-05-28 16:24:15,034 [DEBUG] - --------------------------- 准备数据 2025-05-28 16:24:15 ---------------------------
2025-05-28 16:24:15,056 [DEBUG] - 🧹 删除运行缓存文件夹：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存
2025-05-28 16:24:15,682 [INFO] - 读取数据中心数据...
2025-05-28 16:24:15,683 [DEBUG] - 🛂 没有因子需要额外的全息字段
2025-05-28 16:24:15,683 [DEBUG] - 🗑️ [策略配置] 需要排除`科创板`
2025-05-28 16:24:15,683 [DEBUG] - 🕒 检测到需要分钟数据：['1030']，需要额外准备pivot数据
2025-05-28 16:24:15,697 [DEBUG] - 📂 读取到股票数量：5132，不包括科创板
2025-05-28 16:24:15,708 [DEBUG] - 🚀 多进程处理数据，进程数量：23
2025-05-28 16:24:57,411 [DEBUG] - 📈 保存股票预处理数据: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\股票预处理数据.pkl
2025-05-28 16:24:57,411 [DEBUG] - 📅 行情数据最新交易日期：2025-05-28 00:00:00
2025-05-28 16:25:01,159 [DEBUG] - 📄 生成行情数据透视表...
2025-05-28 16:25:01,160 [DEBUG] - ⚗️ 合成整体市场数据...
2025-05-28 16:25:03,986 [DEBUG] - [1/4] 开盘价透视表...
2025-05-28 16:25:07,002 [DEBUG] - [2/4] 收盘价透视表...
2025-05-28 16:25:10,054 [DEBUG] - [3/4] 前收盘价透视表...
2025-05-28 16:25:13,094 [DEBUG] - [4/4] 1030透视表...
2025-05-28 16:25:16,459 [DEBUG] - 🗄️ 保存行情数据透视表: D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\全部股票行情pivot.pkl
2025-05-28 16:25:16,901 [OK] - 数据准备耗时：61.22 秒
2025-05-28 16:25:17,925 [DEBUG] - --------------------------- 因子计算 2025-05-28 16:25:17 ---------------------------
2025-05-28 16:25:17,946 [INFO] - 因子计算...
2025-05-28 16:25:17,946 [DEBUG] - 🛂 配置信息检查...
2025-05-28 16:25:17,946 [DEBUG] - ℹ️ 检测到没有财务因子
2025-05-28 16:25:17,946 [DEBUG] - 🔍 检测到外部数据：['5min_close']
2025-05-28 16:25:17,948 [DEBUG] - 💿 读取股票K线数据...
2025-05-28 16:25:22,540 [DEBUG] - * 总共计算因子个数：7 个
* 单次计算因子个数：32 个，(需分成1组计算)
* 需要计算币种数量：5077 个
2025-05-28 16:25:22,540 [DEBUG] - 🚀 多进程计算因子，进程数量：23
2025-05-28 16:25:22,540 [DEBUG] - 🗂️ 因子分片计算中，进度：1/1
2025-05-28 16:25:48,916 [DEBUG] - 📅 因子结果最晚日期：2025-05-27 00:00:00
2025-05-28 16:25:51,133 [DEBUG] - 💾 存储因子数据...
2025-05-28 16:25:51,133 [DEBUG] - - D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\all_factors_kline.pkl
2025-05-28 16:25:51,142 [DEBUG] - 最晚交易日期：2025-05-27 00:00:00
2025-05-28 16:25:53,234 [OK] - 因子计算完成，耗时：35.29秒
2025-05-28 16:25:54,562 [DEBUG] - --------------------------- 条件选股 2025-05-28 16:25:54 ---------------------------
2025-05-28 16:25:54,602 [DEBUG] - 🔍 因子文件：D:\25分享会实盘客户端-股票\select-stock-pro_v1.4.2a\data\运行缓存\all_factors_kline.pkl
2025-05-28 16:25:56,194 [DEBUG] - 🎯 #0.KDJ_CMO熊市策略 选股启动...
2025-05-28 16:25:56,660 [DEBUG] - 📦 [#0.KDJ_CMO熊市策略] 选股数据加载完成，最晚日期：2025-05-27 00:00:00
2025-05-28 16:25:58,629 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 数据清洗完成，去掉空因子数据，最晚日期：2025-05-27 00:00:00
2025-05-28 16:25:59,463 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 前置筛选耗时：0.83s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 16:26:06,684 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 选股复合因子计算耗时：7.21s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 16:26:10,042 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 定风波择时：3.36s
2025-05-28 16:26:13,793 [DEBUG] - ➡️ [#0.KDJ_CMO熊市策略] 选股耗时：3.75s。数据最晚日期：2025-05-26 00:00:00
2025-05-28 16:26:13,816 [DEBUG] - 🏁 [#0.KDJ_CMO熊市策略] 选股耗时: 17.62s
2025-05-28 16:26:14,114 [DEBUG] - 🔍 计算`#0.KDJ_CMO熊市策略`最新选股结果, 数据最晚选股日：2025-05-26 00:00:00
2025-05-28 16:26:14,140 [DEBUG] - 💾 选股结果数据大小：1.9292 MB
2025-05-28 16:26:14,140 [OK] - 选股完成，总耗时：19.557秒
2025-05-28 16:26:14,140 [DEBUG] - --------------------------- 模拟交易 2025-05-28 16:26:14 ---------------------------
2025-05-28 16:26:14,163 [DEBUG] - 🔀 持仓周期权重聚合...
2025-05-28 16:26:15,188 [DEBUG] - 👌 权重聚合完成，耗时：1.026秒
2025-05-28 16:26:15,198 [DEBUG] - 🗓️ 回测模拟区间:2015-01-05~2025-05-27，选股结果区间:2015-01-05~2025-05-26
2025-05-28 16:26:15,409 [INFO] - 开始模拟日线交易...
2025-05-28 16:26:18,032 [DEBUG] - ℹ️ 实际模拟资金:100,000.00(整体使用率:100.00%), 印花税率:0.10%, 券商佣金费率:0.01%
2025-05-28 16:26:18,530 [DEBUG] - 🎯 开始模拟交易...
2025-05-28 16:26:25,605 [OK] - 完成模拟交易，花费时间: 7.075秒
2025-05-28 16:26:25,668 [DEBUG] - 📈 策略评价 --------------------------------
                                       0
累积净值                            6.33
年化收益                          19.42%
最大回撤                         -27.50%
最大回撤开始时间     2017-10-10 00:00:00
最大回撤结束时间     2018-10-29 00:00:00
年化收益/回撤比                     0.71
修复涨幅（均/最大）       5.59% / 37.94%
修复时间（均/最大）        111.9 / 661.5
盈利周期数                        1114.0
亏损周期数                        1410.0
胜率（含0/去0）          44.12% / 52.08%
每周期平均收益                     0.08%
盈亏收益比                          1.73
单周期最大盈利                     8.48%
单周期大亏损                      -6.08%
最大连续盈利周期数                  12.0
最大连续亏损周期数                  19.0
收益率标准差                       0.84%

📊 分年收益率 --------------------------------
             涨跌幅
交易日期           
2015-12-31  103.68%
2016-12-31   31.13%
2017-12-31    3.14%
2018-12-31  -19.63%
2019-12-31   23.57%
2020-12-31   30.35%
2021-12-31    8.12%
2022-12-31   10.27%
2023-12-31   -2.84%
2024-12-31   41.17%
2025-12-31    8.56%
2025-05-28 16:26:25,668 [DEBUG] - 💰 总手续费: ￥101,050.55

2025-05-28 16:26:25,668 [INFO] - 开始绘制资金曲线...
